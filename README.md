# Goali: AI-Powered Self-Development Platform

<!-- TypeScript fixes applied for Digital Ocean deployment -->

Goali is a multi-modal platform designed to provide personalized guidance and support for self-development using an advanced AI agent system.

## Core Goal

To empower users on their self-improvement journey by offering tailored insights, activities, and tracking through an engaging and interactive experience.

## Key Features

*   Personalized AI coaching and guidance
*   Activity tracking and progress monitoring
*   Mood analysis and reflection tools
*   Interactive user interface (Web-based initially)
*   Real-time communication via WebSockets

## Technology Stack

*   **Backend:** Python, Django, Django Channels, LangGraph, Celery, PostgreSQL, Redis
*   **Frontend:** TypeScript, React (v19+), WebSockets
*   **Infrastructure:** Docker, Prometheus (Monitoring)

## Getting Started

Detailed setup instructions for developers can be found in:

*   Backend: [`backend/README_DEV.md`](./backend/README_DEV.md)
*   Frontend: [`frontend/README.md`](./frontend/README.md)

## Running the Application

Basic commands to start the development environment (refer to linked READMEs for details):

*   **Backend:** (Typically involves <PERSON><PERSON> Compose) `docker-compose up --build` (inside `backend/`)
*   **Frontend:** `npm start` (inside `frontend/`)

## Running Tests

*   **Backend:** `pytest` or `./run-tests.sh` (inside `backend/`) - See [`backend/README_TESTING.md`](./backend/README_TESTING.md) for details.
*   **Frontend:** `npm test` (inside `frontend/`)

## Project Structure

*   `backend/`: Contains the Django application, AI agents, and related backend code.
*   `frontend/`: Contains the React web application code.
*   `docs/`: Project documentation, including API contracts, architectural decisions, and agent details.
*   `goali-governance/`: Contains the project's governance system documents (values, standards, decision logs, operational files). See `goali-governance/README.md` for usage.
*   `scripts/`: Utility scripts for development or maintenance.
*   `monitoring/`: Configuration for monitoring tools (e.g., Prometheus).

## Key Documentation & Guidance

Understanding the project requires familiarity with these core documents:

*   **For AI & Developers:**
    *   [`PLANNING.md`](./PLANNING.md): Project architecture, goals, and design rationale.
    *   [`AI_CODING_INSTRUCTIONS.md`](./AI_CODING_INSTRUCTIONS.md): **Crucial rules** for AI code generation and contribution standards.
    *   [`TASK.md`](./TASK.md): Current development tasks and priorities for the AI.
    *   [`docs/ApiContract.md`](./docs/ApiContract.md): Specification for WebSocket communication between frontend and backend.
    *   [`CONTRIBUTING.md`](./CONTRIBUTING.md): Guidelines for human contributors (PR process, etc.).
*   **Developer Deep Dives:**
    *   [`docs/DOCUMENTATION_MAP.md`](./docs/DOCUMENTATION_MAP.md): Central map of all project documentation.
    *   [`backend/DEVELOPER_GUIDE.md`](./backend/DEVELOPER_GUIDE.md): Comprehensive backend development guide.
    *   [`frontend/README.md`](./frontend/README.md): Detailed frontend setup and development guide.
    *   [`backend/README_TESTING.md`](./backend/README_TESTING.md): In-depth backend testing strategies.
    *   [`docs/`](./docs/): Explore subdirectories for models, agents, and more.

## License

This project is licensed under the terms specified in the [`LICENSE`](./LICENSE) file.
