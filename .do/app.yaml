name: goali-app-2
region: nyc

services:
- name: backend
  source_dir: /
  github:
    repo: elgui/goali
    branch: gguine/prod-do
    deploy_on_push: true
  dockerfile_path: backend/Dockerfile.production
  http_port: 8000
  instance_count: 1
  instance_size_slug: apps-s-1vcpu-1gb
  routes:
  - path: /api
  - path: /admin
  - path: /health
  - path: /ws
  health_check:
    http_path: /health/
    initial_delay_seconds: 60
    period_seconds: 30
    timeout_seconds: 10
    success_threshold: 1
    failure_threshold: 3
  envs:
  - key: DJANGO_SETTINGS_MODULE
    value: config.settings.production
  - key: DEBUG
    value: "False"
  - key: ALLOWED_HOSTS
    value: "goali-app-2-*.ondigitalocean.app,localhost,127.0.0.1"
  - key: DATABASE_URL
    value: ${db.DATABASE_URL}
  - key: REDIS_URL
    value: redis://localhost:6379/0
  - key: CELERY_BROKER_URL
    value: redis://localhost:6379/0
  - key: CELERY_RESULT_BACKEND
    value: redis://localhost:6379/1
  - key: SECRET_KEY
    value: ${SECRET_KEY}
    type: SECRET
  - key: MISTRAL_API_KEY
    value: ${MISTRAL_API_KEY}
    type: SECRET
  - key: SPACES_ACCESS_KEY
    value: ${SPACES_ACCESS_KEY}
    type: SECRET
  - key: SPACES_SECRET_KEY
    value: ${SPACES_SECRET_KEY}
    type: SECRET
  - key: SPACES_BUCKET_NAME
    value: goali-static-assets
  - key: SPACES_REGION
    value: nyc3
  - key: SPACES_CDN_ENDPOINT
    value: goali-static-assets.nyc3.cdn.digitaloceanspaces.com
  - key: DEFAULT_LLM_MODEL_NAME
    value: mistral-small-latest
  - key: DEFAULT_LLM_TEMPERATURE
    value: "0.7"
  - key: GOALI_DEFAULT_EXECUTION_MODE
    value: production

workers:
- name: celery-worker
  source_dir: /
  github:
    repo: elgui/goali
    branch: gguine/prod-do
    deploy_on_push: true
  dockerfile_path: backend/Dockerfile.celery
  instance_count: 1
  instance_size_slug: apps-s-1vcpu-1gb
  envs:
  - key: DJANGO_SETTINGS_MODULE
    value: config.settings.production
  - key: DEBUG
    value: "False"
  - key: DATABASE_URL
    value: ${db.DATABASE_URL}
  - key: REDIS_URL
    value: redis://localhost:6379/0
  - key: CELERY_BROKER_URL
    value: redis://localhost:6379/0
  - key: CELERY_RESULT_BACKEND
    value: redis://localhost:6379/1
  - key: SECRET_KEY
    value: ${SECRET_KEY}
    type: SECRET
  - key: MISTRAL_API_KEY
    value: ${MISTRAL_API_KEY}
    type: SECRET

databases:
- name: db
  engine: PG
  version: "15"
  production: false
  cluster_name: goali-db-cluster
  db_name: db
  db_user: db

static_sites:
- name: frontend
  source_dir: frontend
  github:
    repo: elgui/goali
    branch: gguine/prod-do
    deploy_on_push: true
  build_command: npm install && npm run build
  output_dir: dist
  index_document: index.html
  error_document: index.html
  routes:
  - path: /
  envs:
  - key: VITE_API_URL
    value: https://goali-app-2-backend.ondigitalocean.app/api
  - key: VITE_WS_URL
    value: wss://goali-app-2-backend.ondigitalocean.app/ws