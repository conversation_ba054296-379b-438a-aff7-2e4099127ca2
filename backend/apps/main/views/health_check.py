"""
Health check endpoint for DigitalOcean App Platform monitoring.
"""
import logging
from django.http import JsonResponse
from django.db import connection
from django.core.cache import cache
from django.utils import timezone
from django.conf import settings

logger = logging.getLogger(__name__)

def health_check(request):
    """
    Comprehensive health check endpoint for App Platform.
    
    Checks:
    - Database connectivity
    - Redis/Cache connectivity
    - Basic application status
    
    Returns:
        JsonResponse: Health status with detailed information
    """
    health_status = {
        'status': 'healthy',
        'timestamp': timezone.now().isoformat(),
        'service': 'goali-backend',
        'version': '1.0.0',
        'checks': {}
    }
    
    overall_healthy = True
    
    # Check database connection
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            if result and result[0] == 1:
                health_status['checks']['database'] = {
                    'status': 'healthy',
                    'message': 'Database connection successful'
                }
            else:
                raise Exception("Database query returned unexpected result")
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        health_status['checks']['database'] = {
            'status': 'unhealthy',
            'message': f'Database connection failed: {str(e)}'
        }
        overall_healthy = False
    
    # Check Redis/Cache connection
    try:
        # Test cache with a simple set/get operation
        test_key = 'health_check_test'
        test_value = f'test_{timezone.now().timestamp()}'
        
        cache.set(test_key, test_value, 30)  # 30 seconds TTL
        retrieved_value = cache.get(test_key)
        
        if retrieved_value == test_value:
            health_status['checks']['cache'] = {
                'status': 'healthy',
                'message': 'Cache connection successful'
            }
            # Clean up test key
            cache.delete(test_key)
        else:
            raise Exception("Cache set/get test failed")
            
    except Exception as e:
        logger.error(f"Cache health check failed: {e}")
        health_status['checks']['cache'] = {
            'status': 'unhealthy',
            'message': f'Cache connection failed: {str(e)}'
        }
        overall_healthy = False
    
    # Check application configuration
    try:
        # Verify critical settings are present
        critical_settings = ['SECRET_KEY', 'DATABASES']
        missing_settings = []
        
        for setting in critical_settings:
            if not hasattr(settings, setting) or not getattr(settings, setting):
                missing_settings.append(setting)
        
        if missing_settings:
            raise Exception(f"Missing critical settings: {missing_settings}")
        
        health_status['checks']['configuration'] = {
            'status': 'healthy',
            'message': 'Application configuration valid'
        }
        
    except Exception as e:
        logger.error(f"Configuration health check failed: {e}")
        health_status['checks']['configuration'] = {
            'status': 'unhealthy',
            'message': f'Configuration check failed: {str(e)}'
        }
        overall_healthy = False
    
    # Set overall status
    if not overall_healthy:
        health_status['status'] = 'unhealthy'
    
    # Return appropriate HTTP status code
    status_code = 200 if overall_healthy else 503
    
    return JsonResponse(health_status, status=status_code)