"""config URL Configuration"""
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.contrib.staticfiles.urls import staticfiles_urlpatterns

# Import the custom admin site INSTANCE
from config.admin import admin_site # Import custom admin instance

# Import health check views
from apps.main.views.health_check import health_check

urlpatterns = [
    # Health check endpoints for DigitalOcean App Platform
    path('health/', health_check, name='health_check'),
    
    # Admin and main app URLs
    path('admin/', admin_site.urls),  # Use the imported custom admin site instance (includes admin_tools URLs now)
    path('', include('apps.main.urls')),  # Main app URLs (already have /api/ prefixes)
    path('coverage/', include('apps.coverage_dashboard.urls')),
    # path('admin-tools/', include('apps.admin_tools.urls')), # Removed - Integrated into admin_site
    # Add other URL patterns here
]

# Explicitly add staticfiles patterns for development
urlpatterns += staticfiles_urlpatterns()

# Serve static files in development
if settings.DEBUG:
    # Add static files from STATIC_ROOT (for admin)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    
    # Also serve from STATICFILES_DIRS if specified
    for staticfiles_dir in settings.STATICFILES_DIRS:
        urlpatterns += static(settings.STATIC_URL, document_root=staticfiles_dir)
    
    # Media files (if applicable)
    if hasattr(settings, 'MEDIA_URL') and settings.MEDIA_ROOT:
        urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
