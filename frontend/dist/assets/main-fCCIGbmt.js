import{f as Le,u as Te,Z as ze,E as xe,i as O,a as R,x as r,T as ye}from"./lit-KavRT6Ey.js";import{m as x}from"./matter-4D3s4xTp.js";import{b as Ue,C as Z,G as J}from"./pixi-DRyRoF6D.js";(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))s(n);new MutationObserver(n=>{for(const a of n)if(a.type==="childList")for(const o of a.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&s(o)}).observe(document,{childList:!0,subtree:!0});function i(n){const a={};return n.integrity&&(a.integrity=n.integrity),n.referrerPolicy&&(a.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?a.credentials="include":n.crossOrigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}function s(n){if(n.ep)return;n.ep=!0;const a=i(n);fetch(n.href,a)}})();/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const j=t=>(e,i)=>{i!==void 0?i.addInitializer(()=>{customElements.define(t,e)}):customElements.define(t,e)};/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const Be={attribute:!0,type:String,converter:Te,reflect:!1,hasChanged:Le},We=(t=Be,e,i)=>{const{kind:s,metadata:n}=i;let a=globalThis.litPropertyMetadata.get(n);if(a===void 0&&globalThis.litPropertyMetadata.set(n,a=new Map),s==="setter"&&((t=Object.create(t)).wrapped=!0),a.set(i.name,t),s==="accessor"){const{name:o}=i;return{set(l){const d=e.get.call(this);e.set.call(this,l),this.requestUpdate(o,d,t)},init(l){return l!==void 0&&this.C(o,void 0,t,l),l}}}if(s==="setter"){const{name:o}=i;return function(l){const d=this[o];e.call(this,l),this.requestUpdate(o,d,t)}}throw Error("Unsupported decorator location: "+s)};function w(t){return(e,i)=>typeof i=="object"?We(t,e,i):((s,n,a)=>{const o=n.hasOwnProperty(a);return n.constructor.createProperty(a,s),o?Object.getOwnPropertyDescriptor(n,a):void 0})(t,e,i)}/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */function c(t){return w({...t,state:!0,attribute:!1})}/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const Fe=(t,e,i)=>(i.configurable=!0,i.enumerable=!0,Reflect.decorate&&typeof e!="object"&&Object.defineProperty(t,e,i),i);/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */function we(t,e){return(i,s,n)=>{const a=o=>o.renderRoot?.querySelector(t)??null;return Fe(i,s,{get(){return a(this)}})}}/**
 * @license
 * Copyright 2020 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const{I:Oe}=ze,Re=t=>t.strings===void 0,ke=()=>document.createComment(""),ee=(t,e,i)=>{const s=t._$AA.parentNode,n=e===void 0?t._$AB:e._$AA;if(i===void 0){const a=s.insertBefore(ke(),n),o=s.insertBefore(ke(),n);i=new Oe(a,o,t,t.options)}else{const a=i._$AB.nextSibling,o=i._$AM,l=o!==t;if(l){let d;i._$AQ?.(t),i._$AM=t,i._$AP!==void 0&&(d=t._$AU)!==o._$AU&&i._$AP(d)}if(a!==n||l){let d=i._$AA;for(;d!==a;){const h=d.nextSibling;s.insertBefore(d,n),d=h}}}return i},N=(t,e,i=t)=>(t._$AI(e,i),t),je={},Ne=(t,e=je)=>t._$AH=e,He=t=>t._$AH,ce=t=>{t._$AP?.(!1,!0);let e=t._$AA;const i=t._$AB.nextSibling;for(;e!==i;){const s=e.nextSibling;e.remove(),e=s}};/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const oe={ATTRIBUTE:1,CHILD:2},re=t=>(...e)=>({_$litDirective$:t,values:e});let le=class{constructor(e){}get _$AU(){return this._$AM._$AU}_$AT(e,i,s){this._$Ct=e,this._$AM=i,this._$Ci=s}_$AS(e,i){return this.update(e,i)}update(e,i){return this.render(...i)}};/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const te=(t,e)=>{const i=t._$AN;if(i===void 0)return!1;for(const s of i)s._$AO?.(e,!1),te(s,e);return!0},ae=t=>{let e,i;do{if((e=t._$AM)===void 0)break;i=e._$AN,i.delete(t),t=e}while(i?.size===0)},Ae=t=>{for(let e;e=t._$AM;t=e){let i=e._$AN;if(i===void 0)e._$AN=i=new Set;else if(i.has(t))break;i.add(t),Ye(e)}};function Ge(t){this._$AN!==void 0?(ae(this),this._$AM=t,Ae(this)):this._$AM=t}function Ve(t,e=!1,i=0){const s=this._$AH,n=this._$AN;if(n!==void 0&&n.size!==0)if(e)if(Array.isArray(s))for(let a=i;a<s.length;a++)te(s[a],!1),ae(s[a]);else s!=null&&(te(s,!1),ae(s));else te(this,t)}const Ye=t=>{t.type==oe.CHILD&&(t._$AP??=Ve,t._$AQ??=Ge)};class qe extends le{constructor(){super(...arguments),this._$AN=void 0}_$AT(e,i,s){super._$AT(e,i,s),Ae(this),this.isConnected=e._$AU}_$AO(e,i=!0){e!==this.isConnected&&(this.isConnected=e,e?this.reconnected?.():this.disconnected?.()),i&&(te(this,e),ae(this))}setValue(e){if(Re(this._$Ct))this._$Ct._$AI(e,this);else{const i=[...this._$Ct._$AH];i[this._$Ci]=e,this._$Ct._$AI(i,this,0)}}disconnected(){}reconnected(){}}/**
 * @license
 * Copyright 2020 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const Xe=()=>new Je;class Je{}const de=new WeakMap,Ke=re(class extends qe{render(t){return xe}update(t,[e]){const i=e!==this.G;return i&&this.G!==void 0&&this.rt(void 0),(i||this.lt!==this.ct)&&(this.G=e,this.ht=t.options?.host,this.rt(this.ct=t.element)),xe}rt(t){if(this.isConnected||(t=void 0),typeof this.G=="function"){const e=this.ht??globalThis;let i=de.get(e);i===void 0&&(i=new WeakMap,de.set(e,i)),i.get(this.G)!==void 0&&this.G.call(this.ht,void 0),i.set(this.G,t),t!==void 0&&this.G.call(this.ht,t)}else this.G.value=t}get lt(){return typeof this.G=="function"?de.get(this.ht??globalThis)?.get(this.G):this.G?.value}disconnected(){this.lt===this.ct&&this.rt(void 0)}reconnected(){this.rt(this.ct)}});function I(t){for(;t<0;)t+=2*Math.PI;for(;t>=2*Math.PI;)t-=2*Math.PI;return t}function ge(t,e,i,s){const n=i-t,a=s-e;return Math.sqrt(n*n+a*a)}function Qe(t){console.log(`[PHYSICS] Creating FIXED 100-segment system for ${t.length} activities`);const e=100,i=2*Math.PI/e,s=t.reduce((g,v)=>g+v.percentage,0),n=t.map(g=>({...g,percentage:g.percentage/s*100,segmentCount:Math.round(g.percentage/s*e)}));let a=n.reduce((g,v)=>g+v.segmentCount,0),o=e-a;const l=[...n].sort((g,v)=>v.segmentCount-g.segmentCount);for(let g=0;g<Math.abs(o);g++){const v=l[g%l.length];v.segmentCount+=o>0?1:-1}console.log("[PHYSICS] Segment distribution:",n.map(g=>`${g.text}: ${g.segmentCount} segments`));const d=[],h=Ze(n);let p=-Math.PI/2;return h.forEach((g,v)=>{const y=n[g],k=p,E=p+i,P=p+i/2;d.push({id:`${y.id}-seg-${v}`,text:y.text,percentage:1,color:y.color,textColor:y.textColor,startAngle:I(k),endAngle:I(E),centerAngle:I(P),activityId:y.id,activityIndex:g,segmentIndex:v}),p=E}),console.log(`[PHYSICS] Created exactly ${d.length} equal segments with ${e} nails`),{segments:d,totalNailCount:e}}function Ze(t){const e=[],i=t.map((s,n)=>({index:n,count:s.segmentCount,color:s.color}));for(;e.length<100;){const s=i.filter(d=>d.count>0);if(s.length===0)break;const n=e.length>0?i[e[e.length-1]].color:null,a=s.filter(d=>d.color!==n),l=(a.length>0?a:s).reduce((d,h)=>h.count>d.count?h:d);e.push(l.index),l.count--}return e}function _e(t){const e=[],i=2*Math.PI/t.nailCount;for(let s=0;s<t.nailCount;s++){const n=s*i,a=t.centerX+(t.radius+t.nailRadius)*Math.cos(n),o=t.centerY+(t.radius+t.nailRadius)*Math.sin(n);e.push({x:a,y:o,angle:n})}return e}function pe(t,e){const i=I(t);for(const s of e)if(s.startAngle>s.endAngle){if(i>=s.startAngle||i<=s.endAngle)return s}else if(i>=s.startAngle&&i<=s.endAngle)return s;return null}function et(t,e,i,s,n,a,o=8){const l=Pe(t,e,i,s),d=ge(t,e,i,s),h=I(l);let p=null;for(const D of n){const W=I(D.startAngle),Q=I(D.endAngle);if(W>Q){if(h>=W||h<=Q){p=D;break}}else if(h>=W&&h<=Q){p=D;break}}let g=null,v=1/0,y=0;if(a&&a.length>0){for(const D of a){const W=ge(t,e,D.x,D.y);W<v&&(v=W,y=D.angle)}v<o+3&&(g=pe(y,n))}let k=null;for(const D of n){const W=tt(h,D.startAngle,D.endAngle),Q=d>=50&&d<=250;if(W&&Q){k=D;break}}const E=pe(l,n);let P=null,U=0,X="angle";p&&k&&p.id===k.id?(P=p,U=1,X="precise"):g&&v<o+3?(P=g,U=.95,X="collision"):p?(P=p,U=.9,X="precise"):k?(P=k,U=.8,X="proximity"):E&&(P=E,U=.6,X="angle");const Ie={ballAngle:h,ballDistance:d,preciseSegment:p?.text||"None",collisionBasedSegment:g?.text||"None",areaBasedSegment:k?.text||"None",angleBasedSegment:E?.text||"None",closestNailDistance:v,closestNailAngle:y,ballPosition:{x:t,y:e},wheelCenter:{x:i,y:s}};return{segment:P,confidence:U,method:X,debugInfo:Ie}}function tt(t,e,i){const s=I(t),n=I(e),a=I(i);return n>a?s>=n||s<=a:s>=n&&s<=a}function Pe(t,e,i,s){return I(Math.atan2(e-s,t-i))}function it(t,e,i,s,n,a,o){if(n<=0)return 0;const l=Math.atan2(e-o,t-a);let h=Math.atan2(s-o,i-a)-l;return h>Math.PI?h-=2*Math.PI:h<-Math.PI&&(h+=2*Math.PI),h/n}function st(t,e,i,s,n){return ge(t,e,i,s)<=n}class Se{constructor(e){this.runner=null,this.ballBody=null,this.nailBodies=[],this.boundaryBodies=[],this.isRunning=!1,this.animationCallbacks=[],this.lastFrameTime=0,this.wheelRotation=0,this.wheelVelocity=0,this.frameCount=0,this.initialNailPositions=[],this.config=e,this.engine=x.Engine.create(),this.world=this.engine.world,this.engine.gravity.y=0,this.engine.gravity.x=0,this.engine.gravity.scale=.001,this.setupCollisionEvents()}initialize(){this.createNails(),this.createBall(),this.setupConstraints()}createNails(){const e=_e(this.config);this.initialNailPositions=[...e],this.nailBodies=e.map((i,s)=>x.Bodies.circle(i.x,i.y,this.config.nailRadius,{isStatic:!0,restitution:.95,friction:.1,render:{visible:!1},collisionFilter:{category:2,mask:4},label:`nail-${s}`})),x.World.add(this.world,this.nailBodies)}createBall(){const e=this.config.centerX,i=this.config.centerY-this.config.ballStartOffset;this.ballBody=x.Bodies.circle(e,i,this.config.ballRadius,{restitution:.95,friction:.1,frictionAir:.005,density:.001,render:{visible:!1},collisionFilter:{category:4,mask:10},label:"ball"}),x.World.add(this.world,this.ballBody)}setupConstraints(){if(!this.ballBody)return;const e=20,i=this.config.radius+this.config.ballRadius+5,s=32,n=2*Math.PI/s;this.boundaryBodies=[];for(let a=0;a<s;a++){const o=a*n,l=this.config.centerX+i*Math.cos(o),d=this.config.centerY+i*Math.sin(o),h=x.Bodies.rectangle(l,d,e,e,{isStatic:!0,restitution:.9,friction:.1,render:{visible:!1},collisionFilter:{category:8,mask:4},label:`boundary-${a}`});this.boundaryBodies.push(h)}x.World.add(this.world,this.boundaryBodies)}setupCollisionEvents(){x.Events.on(this.engine,"collisionStart",e=>{e.pairs.forEach(i=>{const{bodyA:s,bodyB:n}=i;(s.label==="ball"&&n.label?.startsWith("nail-")||n.label==="ball"&&s.label?.startsWith("nail-"))&&this.handleBallNailCollision(s,n)})}),x.Events.on(this.engine,"beforeUpdate",()=>{this.updatePhysics()})}handleBallNailCollision(e,i){const s=e.label==="ball"?e:i,n=e.label==="ball"?i:e,o=.002+Math.abs(this.wheelVelocity)*.001,l=s.position.x-n.position.x,d=s.position.y-n.position.y,h=Math.sqrt(l*l+d*d);if(h>0){const p=l/h,g=d/h,v={x:p*o+(Math.random()-.5)*.001,y:g*o+(Math.random()-.5)*.001};x.Body.applyForce(s,s.position,v);const y=(Math.random()-.5)*.05;x.Body.setAngularVelocity(s,s.angularVelocity+y)}this.emitCollisionEvent(s,n)}emitCollisionEvent(e,i){console.log("Ball-nail collision detected")}updateNailPositions(){this.nailBodies.length===0||this.initialNailPositions.length===0||Math.abs(this.wheelVelocity)<.001||this.nailBodies.forEach((e,i)=>{const s=this.initialNailPositions[i];if(!s)return;const n=s.angle+this.wheelRotation,a=this.config.centerX+(this.config.radius+this.config.nailRadius)*Math.cos(n),o=this.config.centerY+(this.config.radius+this.config.nailRadius)*Math.sin(n);x.Body.setPosition(e,{x:a,y:o})})}updatePhysics(){if(!this.ballBody)return;const e=16;this.wheelRotation+=this.wheelVelocity*(e/1e3),Math.abs(this.wheelVelocity)>.001?this.wheelVelocity*=1-this.config.wheelRotationFriction:this.wheelVelocity=0,this.updateNailPositions();const i=performance.now(),s=i-this.lastFrameTime;this.lastFrameTime=i;const n=this.getBallVelocity(),a=n.magnitude,o={timestamp:i,deltaTime:s,ballPosition:{...this.ballBody.position},ballVelocity:n,ballAngle:this.getBallAngle(),wheelRotation:this.wheelRotation,wheelVelocity:this.wheelVelocity};if(this.frameCount%30===0&&(a>.01||Math.abs(this.wheelVelocity)>.01)){const l=Math.sqrt(Math.pow(this.ballBody.position.x-this.config.centerX,2)+Math.pow(this.ballBody.position.y-this.config.centerY,2));console.log(`[PHYSICS] Ball: (${this.ballBody.position.x.toFixed(1)}, ${this.ballBody.position.y.toFixed(1)}) Speed: ${a.toFixed(3)} Distance: ${l.toFixed(1)} WheelVel: ${this.wheelVelocity.toFixed(3)}`),l>this.config.radius+50&&console.warn(`[PHYSICS] Ball is outside expected bounds! Distance: ${l.toFixed(1)}, Max expected: ${this.config.radius+50}`)}this.frameCount=(this.frameCount||0)+1,this.animationCallbacks.forEach(l=>l(o))}start(){this.isRunning||(this.runner=x.Runner.create(),x.Runner.run(this.runner,this.engine),this.isRunning=!0,this.lastFrameTime=performance.now(),console.log("[PHYSICS] Physics simulation started with animation loop"))}stop(){!this.isRunning||!this.runner||(x.Runner.stop(this.runner),this.runner=null,this.isRunning=!1,console.log("[PHYSICS] Physics simulation stopped"))}spinWheel(e){console.log(`[PHYSICS] Spinning VERTICAL wheel with force: ${e} for ~10 second duration`),this.wheelVelocity=e*this.config.initialWheelVelocity,console.log(`[PHYSICS] Wheel velocity set to: ${this.wheelVelocity} (target: 10s duration)`),this.engine.gravity.y=.7,console.log("[PHYSICS] GRAVITY ENABLED - ball will now fall as wheel spins"),this.ballBody&&(console.log(`[PHYSICS] Ball position before release: (${this.ballBody.position.x.toFixed(1)}, ${this.ballBody.position.y.toFixed(1)})`),console.log("[PHYSICS] Ball released to fall STRAIGHT DOWN - no horizontal forces applied"),x.Body.setVelocity(this.ballBody,{x:0,y:0}),x.Body.setAngularVelocity(this.ballBody,0),console.log("[PHYSICS] Ball will fall vertically due to gravity only when wheel spins. Wheel spins for ~10 seconds."))}spinBall(e,i=0){this.spinWheel(e)}getBallAngle(){return this.ballBody?Pe(this.ballBody.position.x,this.ballBody.position.y,this.config.centerX,this.config.centerY):0}getBallPosition(){return this.ballBody?{x:this.ballBody.position.x,y:this.ballBody.position.y}:{x:this.config.centerX,y:this.config.centerY-this.config.ballStartOffset}}getBallVelocity(){if(!this.ballBody)return{x:0,y:0,magnitude:0};const e=this.ballBody.velocity;return{x:e.x,y:e.y,magnitude:x.Vector.magnitude(e)}}isBallSettled(e=.01){return this.getBallVelocity().magnitude<e}isWheelSettled(e=.01){return Math.abs(this.wheelVelocity)<e}getWheelRotation(){return this.wheelRotation}getWheelVelocity(){return this.wheelVelocity}getBallBody(){return this.ballBody}getNailPositions(){return this.initialNailPositions.map(e=>{const i=e.angle+this.wheelRotation,s=this.config.centerX+(this.config.radius+this.config.nailRadius)*Math.cos(i),n=this.config.centerY+(this.config.radius+this.config.nailRadius)*Math.sin(i);return{x:s,y:n,angle:i}})}isSettled(e=.01){const i=this.isBallSettled(e),s=Math.abs(this.wheelVelocity)<e;return i&&s}resetBall(){if(!this.ballBody)return;const e=this.config.centerX,i=this.config.centerY-this.config.ballStartOffset;console.log(`[PHYSICS] Resetting ball to TOP of vertical wheel: (${e}, ${i})`),x.Body.setPosition(this.ballBody,{x:e,y:i}),x.Body.setVelocity(this.ballBody,{x:0,y:0}),x.Body.setAngularVelocity(this.ballBody,0),this.engine.gravity.y=0,console.log("[PHYSICS] GRAVITY DISABLED - ball will not fall until wheel spins"),this.wheelRotation=0,this.wheelVelocity=0,this.frameCount=0,console.log(`[PHYSICS] Ball reset complete. Ball suspended until wheel spins. Position: (${this.ballBody.position.x}, ${this.ballBody.position.y})`)}onAnimationFrame(e){this.animationCallbacks.push(e)}removeAnimationFrame(e){const i=this.animationCallbacks.indexOf(e);i>-1&&this.animationCallbacks.splice(i,1)}getPhysicsState(){return{engine:this.engine,world:this.world,wheelBody:null,ballBody:this.ballBody,nailBodies:this.nailBodies,isRunning:this.isRunning,ballAngularVelocity:this.ballBody?.angularVelocity||0}}destroy(){this.stop(),x.World.clear(this.world,!1),x.Engine.clear(this.engine),this.animationCallbacks=[],this.ballBody=null,this.nailBodies=[],this.boundaryBodies=[],this.wheelRotation=0,this.wheelVelocity=0}}function ue(t){if(t=t.replace("#",""),t.length===3&&(t=t.split("").map(n=>n+n).join("")),t.length!==6)return null;const e=parseInt(t.substr(0,2),16),i=parseInt(t.substr(2,2),16),s=parseInt(t.substr(4,2),16);return isNaN(e)||isNaN(i)||isNaN(s)?null:{r:e,g:i,b:s}}function me(t,e,i){const s=n=>{const a=Math.round(Math.max(0,Math.min(255,n))).toString(16);return a.length===1?"0"+a:a};return`#${s(t)}${s(e)}${s(i)}`}function nt(t,e,i){t/=255,e/=255,i/=255;const s=Math.max(t,e,i),n=Math.min(t,e,i);let a=0,o=0;const l=(s+n)/2;if(s!==n){const d=s-n;switch(o=l>.5?d/(2-s-n):d/(s+n),s){case t:a=(e-i)/d+(e<i?6:0);break;case e:a=(i-t)/d+2;break;case i:a=(t-e)/d+4;break}a/=6}return{h:a*360,s:o*100,l:l*100}}function fe(t,e,i){t/=360,e/=100,i/=100;const s=(l,d,h)=>(h<0&&(h+=1),h>1&&(h-=1),h<1/6?l+(d-l)*6*h:h<1/2?d:h<2/3?l+(d-l)*(2/3-h)*6:l);let n,a,o;if(e===0)n=a=o=i;else{const l=i<.5?i*(1+e):i+e-i*e,d=2*i-l;n=s(d,l,t+1/3),a=s(d,l,t),o=s(d,l,t-1/3)}return{r:Math.round(n*255),g:Math.round(a*255),b:Math.round(o*255)}}function at(t,e=0){const i=[];for(let n=0;n<t;n++){const a=(e+n*137.508)%360,o=65+n%3*10,l=50+n%2*15,d=fe(a,o,l);i.push(me(d.r,d.g,d.b))}return i}function ot(t){if(t.length<=1)return t;const e=[...t],i=80;for(let s=0;s<e.length;s++){const n=e[s],a=(s+1)%e.length,o=e[a];if(Ce(n,o)<i){const l=ue(o);if(l){const d=nt(l.r,l.g,l.b);let h={...d};h.h=(d.h+90)%360;let p=fe(h.h,h.s,h.l),g=me(p.r,p.g,p.b);Ce(n,g)<i&&(h.s=Math.max(30,Math.min(90,h.s+(h.s>50?-30:30))),h.l=Math.max(25,Math.min(75,h.l+(h.l>50?-25:25))),p=fe(h.h,h.s,h.l),g=me(p.r,p.g,p.b)),e[a]=g}}}return e}function Ce(t,e){const i=ue(t),s=ue(e);if(!i||!s)return 0;const n=(i.r+s.r)/2,a=i.r-s.r,o=i.g-s.g,l=i.b-s.b,d=2+n/256,h=4,p=2+(255-n)/256;return Math.sqrt(d*a*a+h*o*o+p*l*l)}function rt(t){return t=t.replace("#",""),t.length===3&&(t=t.split("").map(e=>e+e).join("")),parseInt(t,16)}class lt{constructor(e){this.segments=[],this.isInitialized=!1,this.upperHalfOpacity=1,this.lastBallPosition=null,this.config=e,this.app=new Ue,this.container=new Z,this.wheelContainer=new Z,this.wheelGraphics=new J,this.segmentsContainer=new Z,this.textContainer=new Z,this.nailsContainer=new Z,this.ballGraphics=new J,this.upperHalfMask=new J}async initialize(e){const i=navigator.userAgent.toLowerCase().includes("firefox"),s=navigator.userAgent.toLowerCase().includes("safari")&&!navigator.userAgent.toLowerCase().includes("chrome");console.log(`[RENDERER] Browser detection: Firefox=${i}, Safari=${s}`),await this.app.init({canvas:e,width:this.config.centerX*2,height:this.config.centerY*2,backgroundColor:1710618,antialias:!0,resolution:window.devicePixelRatio||1,autoDensity:!0,preference:"webgl",powerPreference:"default",premultipliedAlpha:!1,preserveDrawingBuffer:!1,failIfMajorPerformanceCaveat:!1}),this.setupContainers(),this.isInitialized=!0}setupContainers(){this.wheelContainer.pivot.set(this.config.centerX,this.config.centerY),this.wheelContainer.position.set(this.config.centerX,this.config.centerY),this.wheelContainer.addChild(this.wheelGraphics),this.wheelContainer.addChild(this.segmentsContainer),this.wheelContainer.addChild(this.nailsContainer),this.setupUpperHalfMask(),this.container.addChild(this.wheelContainer),this.container.addChild(this.textContainer),this.container.addChild(this.ballGraphics),this.container.addChild(this.upperHalfMask),this.app.stage.addChild(this.container)}renderSegments(e){this.segments=e,this.segmentsContainer.removeChildren(),e.forEach((i,s)=>{this.renderSegment(i,s)})}renderSegment(e,i){const s=new J,n=rt(e.color);s.moveTo(this.config.centerX,this.config.centerY),s.arc(this.config.centerX,this.config.centerY,this.config.radius,e.startAngle,e.endAngle),s.closePath(),s.fill(n),s.stroke({color:16777215,width:1}),this.segmentsContainer.addChild(s)}renderSegmentText(e){this.textContainer.removeChildren()}renderNails(){this.nailsContainer.removeChildren(),_e(this.config).forEach(i=>{const s=new J;s.circle(0,0,this.config.nailRadius),s.fill(12632256),s.stroke({color:8421504,width:1}),s.x=i.x,s.y=i.y,this.nailsContainer.addChild(s)})}renderBall(e,i){const s=this.lastBallPosition||{x:0,y:0};(Math.sqrt((e-s.x)**2+(i-s.y)**2)>5||!this.lastBallPosition)&&(console.log(`[RENDERER] 🎱 Rendering ball at: (${e.toFixed(1)}, ${i.toFixed(1)})`),this.lastBallPosition={x:e,y:i}),this.ballGraphics.clear();const a=this.config.ballRadius*1.2;this.ballGraphics.circle(e,i,a),this.ballGraphics.fill(16711680),this.ballGraphics.circle(e-a*.3,i-a*.3,a*.4),this.ballGraphics.fill(16777215),this.ballGraphics.circle(e,i,a),this.ballGraphics.stroke({color:0,width:3}),this.ballGraphics.circle(e,i,a+2),this.ballGraphics.stroke({color:16729156,width:1,alpha:.5})}updateBall(e){this.renderBall(e.ballPosition.x,e.ballPosition.y)}updateWheelRotation(e){this.wheelContainer.rotation=e}updateFromFrame(e){this.updateBall(e),this.updateWheelRotation(e.wheelRotation)}renderWheelBackground(){this.wheelGraphics.clear(),this.wheelGraphics.circle(this.config.centerX,this.config.centerY,this.config.radius+10),this.wheelGraphics.stroke({color:4473924,width:12}),this.wheelGraphics.circle(this.config.centerX,this.config.centerY,20),this.wheelGraphics.fill(3355443),this.wheelGraphics.stroke({color:5592405,width:2})}highlightSegment(e){const i=new J;i.moveTo(this.config.centerX,this.config.centerY),i.arc(this.config.centerX,this.config.centerY,this.config.radius+5,e.startAngle,e.endAngle),i.closePath(),i.fill(16766720),i.stroke({color:16753920,width:4}),this.segmentsContainer.addChild(i),setTimeout(()=>{i.parent&&i.parent.removeChild(i)},2e3)}addSpinEffect(){}removeSpinEffect(){}resize(e,i){if(!this.app||!this.app.renderer||!this.isInitialized){console.warn("Cannot resize: PixiJS app not initialized");return}try{this.app.renderer.resize(e,i),this.config.centerX=e/2,this.config.centerY=i/2,this.config.radius=e/2*.8,this.renderWheelBackground(),this.renderNails(),this.segments.length>0&&this.renderSegments(this.segments)}catch(s){console.error("Error resizing renderer:",s)}}getApp(){return this.app}getContainer(){return this.container}getRendererState(){return{app:this.app,container:this.container,wheelGraphics:this.wheelGraphics,segmentsContainer:this.segmentsContainer,textContainer:this.textContainer,nailsContainer:this.nailsContainer,ballGraphics:this.ballGraphics,isInitialized:this.isInitialized}}setupUpperHalfMask(){this.updateUpperHalfMask()}updateUpperHalfMask(){this.upperHalfMask.clear(),this.upperHalfMask.rect(0,0,this.config.centerX*2,this.config.centerY),this.upperHalfMask.fill({color:0,alpha:1-this.upperHalfOpacity})}hideUpperHalf(e){this.upperHalfOpacity=Math.max(0,1-e),this.updateUpperHalfMask()}showUpperHalf(){this.upperHalfOpacity=1,this.updateUpperHalfMask()}destroy(){this.container.removeChildren(),this.app.ticker.stop(),this.app.destroy({removeView:!0},{children:!0,texture:!0,textureSource:!0}),this.isInitialized=!1}}const $e={radius:200,centerX:250,centerY:250,nailCount:100,nailRadius:1.6,ballRadius:8,ballStartOffset:120,spinForce:.15,maxSpinDuration:12e3,wheelFriction:.001,airResistance:.999,wheelRotationFriction:.05,initialWheelVelocity:5};function ct(t){return typeof t=="object"&&t!==null&&Array.isArray(t.segments)&&t.segments.length>0&&t.segments.every(e=>typeof e=="object"&&e!==null&&typeof e.id=="string"&&(typeof e.text=="string"||typeof e.name=="string")&&typeof e.percentage=="number"&&typeof e.color=="string")}var dt=Object.defineProperty,ht=Object.getOwnPropertyDescriptor,$=(t,e,i,s)=>{for(var n=s>1?void 0:s?ht(e,i):e,a=t.length-1,o;a>=0;a--)(o=t[a])&&(n=(s?o(e,i,n):o(n))||n);return s&&n&&dt(e,i,n),n};let S=class extends R{constructor(){super(...arguments),this.wheelData=null,this.config={},this.autoSpin=!1,this.spinDelay=1e3,this.hideUI=!1,this.disableInteraction=!1,this.invisible=!1,this.isSpinning=!1,this.statusMessage="",this.errorMessage="",this.segments=[],this.currentZoom=1,this.isZooming=!1,this.showWinningModal=!1,this.winningSegment=null,this.showWinningAnimation=!1,this.canvasRef=Xe(),this.physicsEngine=null,this.renderer=null,this.wheelConfig=$e,this.spinState=null,this.interactionData=null,this.resizeObserver=null,this.closeWinningModal=()=>{this.showWinningModal=!1,this.winningSegment=null},this.handleModalBackdropClick=t=>{t.target===t.currentTarget&&this.closeWinningModal()},this.startActivity=()=>{this.winningSegment&&this.dispatchEvent(new CustomEvent("activity-start",{detail:{segment:this.winningSegment}})),this.closeWinningModal()},this.spinAgain=()=>{this.closeWinningModal(),setTimeout(()=>{this.spin()},300)}}connectedCallback(){super.connectedCallback(),this.setupResizeObserver()}disconnectedCallback(){super.disconnectedCallback(),this.cleanup()}willUpdate(t){t.has("wheelData")&&this.wheelData&&(this.processWheelData(),this.invisible=!1),t.has("config")&&this.updateConfig(),t.has("invisible")&&!this.invisible&&!this.physicsEngine&&setTimeout(()=>this.initializeWheel(),0)}firstUpdated(){this.invisible||this.initializeWheel()}processWheelData(){if(!this.wheelData||!ct(this.wheelData)){this.errorMessage="Invalid wheel data provided",console.log("[WHEEL] Invalid wheel data:",this.wheelData);return}try{console.log("[WHEEL] Processing wheel data with FIXED 100-segment system..."),console.log("[WHEEL] Input segments:",this.wheelData.segments);const t=this.wheelData.segments.map(s=>({id:s.id,text:s.text||s.name||"Unknown Activity",percentage:s.percentage,color:s.color,activityId:s.activityId||s.activity_tailored_id||s.id}));console.log("[WHEEL] Normalized segments:",t);const{segments:e,totalNailCount:i}=Qe(t);this.segments=e,console.log("[WHEEL] Generated segments count:",e.length),console.log("[WHEEL] First few segments:",e.slice(0,5)),this.wheelConfig.nailCount=i,console.log(`[WHEEL] FIXED system: ${i} nails for ${e.length} equal segments`),this.assignSegmentColors(),this.renderer&&(console.log("[WHEEL] Re-rendering wheel with new segments..."),this.renderWheel()),this.physicsEngine&&console.log("[WHEEL] Updating physics engine with new segments..."),this.errorMessage=""}catch(t){this.errorMessage=`Error processing wheel data: ${t}`,console.error("Wheel data processing error:",t)}}assignSegmentColors(){if(this.segments.filter(e=>!e.color).length>0){const e=at(this.segments.length),i=ot(e);let s=0;this.segments.forEach(n=>{n.color||(n.color=i[s%i.length],s++)})}}updateConfig(){this.wheelConfig={...$e,...this.config}}setupResizeObserver(){window.ResizeObserver&&(this.resizeObserver=new ResizeObserver(()=>{this.handleResize()}),this.resizeObserver.observe(this))}handleResize(){try{const t=this.getBoundingClientRect();if(console.log(`[WHEEL] Component rect: ${t.width}x${t.height}`),t.width===0||t.height===0){console.log("[WHEEL] Component not yet rendered or hidden");return}const e=Math.min(t.width,t.height);console.log(`[WHEEL] Calculated size: ${e}`),this.wheelConfig.centerX=e/2,this.wheelConfig.centerY=e/2,this.wheelConfig.radius=e/2*.8,console.log(`[WHEEL] Updated config: center(${this.wheelConfig.centerX}, ${this.wheelConfig.centerY}), radius=${this.wheelConfig.radius}`),this.renderer&&(this.renderer.resize(e,e),console.log(`[WHEEL] Renderer resized to ${e}x${e}`)),this.physicsEngine&&(console.log("[WHEEL] Updating physics engine configuration..."),this.physicsEngine.destroy(),this.physicsEngine=new Se(this.wheelConfig),this.physicsEngine.initialize(),this.physicsEngine.start(),this.setupPhysicsRendererIntegration(),console.log("[WHEEL] Physics engine updated"))}catch(t){console.error("Error handling resize:",t)}}async initializeWheel(){if(console.log("[WHEEL] Starting wheel initialization..."),this.physicsEngine&&this.renderer){console.log("[WHEEL] Already initialized, skipping...");return}if(!this.canvasRef.value){this.errorMessage="Canvas element not found",console.error("[WHEEL] Canvas element not found!");return}try{this.handleResize(),console.log(`[WHEEL] Canvas sized to: ${this.wheelConfig.centerX*2}x${this.wheelConfig.centerY*2}`),console.log(`[WHEEL] Wheel config: center(${this.wheelConfig.centerX}, ${this.wheelConfig.centerY}), radius=${this.wheelConfig.radius}`),console.log("[WHEEL] Initializing physics engine..."),this.physicsEngine=new Se(this.wheelConfig),this.physicsEngine.initialize(),console.log("[WHEEL] Physics engine initialized"),this.physicsEngine.resetBall(),console.log("[WHEEL] Ball reset to initial position with gravity disabled"),console.log("[WHEEL] Initializing renderer..."),this.renderer=new lt(this.wheelConfig),await this.renderer.initialize(this.canvasRef.value),console.log("[WHEEL] Renderer initialized"),this.setupPhysicsRendererIntegration(),console.log("[WHEEL] Physics-renderer integration set up"),this.renderWheel(),console.log("[WHEEL] Initial wheel rendered"),this.physicsEngine.start(),console.log("[WHEEL] Physics simulation started"),this.autoSpin&&setTimeout(()=>this.spin(),this.spinDelay),console.log("[WHEEL] Wheel initialization complete!")}catch(t){this.errorMessage=`Failed to initialize wheel: ${t}`,console.error("Wheel initialization error:",t)}}setupPhysicsRendererIntegration(){!this.physicsEngine||!this.renderer||this.physicsEngine.onAnimationFrame(t=>{this.renderer.updateFromFrame(t),this.updateSpinState(t),this.updateProgressiveZoom(t)})}renderWheel(){if(!this.renderer)return;this.renderer.renderWheelBackground(),this.renderer.renderNails(),this.segments.length>0&&this.renderer.renderSegments(this.segments);const t=this.physicsEngine?.getBallPosition();t&&(console.log(`[WHEEL] 🎱 Rendering initial ball position at: (${t.x}, ${t.y})`),this.renderer.renderBall(t.x,t.y))}render(){return this.invisible?r`
        <div class="wheel-container waiting">
          <div class="waiting-message">
            <div class="spinner"></div>
            <p>Waiting for wheel data...</p>
          </div>
        </div>
      `:r`
      <div class="wheel-container">
        <canvas ${Ke(this.canvasRef)} class="wheel-canvas"
                @pointerdown=${this.handlePointerDown}
                @pointermove=${this.handlePointerMove}
                @pointerup=${this.handlePointerUp}
                @pointercancel=${this.handlePointerUp}>
        </canvas>

        <div class="wheel-status ${this.statusMessage?"visible":""}">
          ${this.statusMessage}
        </div>

        <!-- Spin button removed - will be replaced with dynamic generate/spin button in parent component -->



        ${this.errorMessage?r`
          <div class="error-message">
            ${this.errorMessage}
          </div>
        `:""}
      </div>

      ${this.showWinningModal&&this.winningSegment?this.renderWinningModal():""}
    `}renderWinningModal(){if(!this.winningSegment)return"";const t=this.winningSegment,e=t.color||"#4ecdc4";return r`
      <div class="winning-modal" @click=${this.handleModalBackdropClick}>
        <div class="winning-modal-content" style="--winning-color: ${e}">
          <button class="winning-close" @click=${this.closeWinningModal}>×</button>

          <div class="winning-confetti">🎉</div>

          <div class="winning-header">
            <h1 class="winning-title">Congratulations!</h1>
            <p class="winning-subtitle">Your wheel has chosen an activity for you</p>
          </div>

          <div class="winning-activity" style="background: ${e}">
            <h2 class="winning-activity-name">${t.text||t.name}</h2>
            <div class="winning-activity-details">
              ${t.description?r`
                <div class="activity-description">
                  <p>${t.description}</p>
                </div>
              `:""}

              <div class="activity-metadata">
                ${t.domain?r`
                  <div class="metadata-item">
                    <span class="metadata-icon">🎯</span>
                    <span class="metadata-label">Domain:</span>
                    <span class="metadata-value">${t.domain}</span>
                  </div>
                `:""}

                ${t.base_challenge_rating?r`
                  <div class="metadata-item">
                    <span class="metadata-icon">⚡</span>
                    <span class="metadata-label">Challenge:</span>
                    <span class="metadata-value">${t.base_challenge_rating}/100</span>
                  </div>
                `:""}

                ${t.percentage?r`
                  <div class="metadata-item">
                    <span class="metadata-icon">📊</span>
                    <span class="metadata-label">Weight:</span>
                    <span class="metadata-value">${t.percentage.toFixed(1)}%</span>
                  </div>
                `:""}
              </div>

              ${t.description?"":r`
                <div class="fallback-message">
                  <p>Get ready for an exciting activity tailored just for you!</p>
                </div>
              `}
            </div>
          </div>

          <div class="winning-stats">
            <div class="winning-stat">
              <div class="winning-stat-label">Activity ID</div>
              <div class="winning-stat-value">${t.activityId||t.id}</div>
            </div>
            <div class="winning-stat">
              <div class="winning-stat-label">Probability</div>
              <div class="winning-stat-value">${t.percentage}%</div>
            </div>
            <div class="winning-stat">
              <div class="winning-stat-label">Category</div>
              <div class="winning-stat-value">Activity</div>
            </div>
          </div>

          <div class="winning-actions">
            <button class="winning-action-btn winning-action-primary" @click=${this.startActivity}>
              Start Activity
            </button>
            <button class="winning-action-btn winning-action-secondary" @click=${this.spinAgain}>
              Spin Again
            </button>
            <button class="winning-action-btn winning-action-secondary" @click=${this.closeWinningModal}>
              Close
            </button>
          </div>
        </div>
      </div>
    `}handlePointerDown(t){if(this.isSpinning)return;const e=this.canvasRef.value.getBoundingClientRect(),i=t.clientX-e.left,s=t.clientY-e.top;st(i,s,this.wheelConfig.centerX,this.wheelConfig.centerY,this.wheelConfig.radius)&&(this.interactionData={type:t.pointerType==="touch"?"touch":"mouse",startPosition:{x:i,y:s},currentPosition:{x:i,y:s},startTime:performance.now(),isActive:!0,velocity:{x:0,y:0}},t.target.setPointerCapture(t.pointerId),this.dispatchEvent(new CustomEvent("wheel-interaction",{detail:{type:this.interactionData.type,position:{x:i,y:s}}})))}handlePointerMove(t){if(!this.interactionData?.isActive)return;const e=this.canvasRef.value.getBoundingClientRect(),i=t.clientX-e.left,s=t.clientY-e.top,a=performance.now()-this.interactionData.startTime;a>0&&(this.interactionData.velocity={x:(i-this.interactionData.currentPosition.x)/a,y:(s-this.interactionData.currentPosition.y)/a}),this.interactionData.currentPosition={x:i,y:s}}handlePointerUp(t){if(!this.interactionData?.isActive)return;const e=performance.now()-this.interactionData.startTime;if(e>100){const i=it(this.interactionData.startPosition.x,this.interactionData.startPosition.y,this.interactionData.currentPosition.x,this.interactionData.currentPosition.y,e,this.wheelConfig.centerX,this.wheelConfig.centerY);Math.abs(i)>.001&&this.spinWithVelocity(i)}this.interactionData=null,t.target.releasePointerCapture(t.pointerId)}setWheelItems(t){console.log("[WHEEL] Setting wheel items via public API:",t);const e={segments:t.map(i=>({id:i.id,text:i.text,percentage:i.percentage,color:i.color||"",activityId:i.activityId||i.id})),wheelId:`wheel-${Date.now()}`,createdAt:new Date().toISOString()};this.wheelData=e,this.invisible=!1,this.requestUpdate()}spin(){if(console.log("[WHEEL] Spin button clicked!"),console.log(`[WHEEL] Current state - isSpinning: ${this.isSpinning}, segments: ${this.segments.length}, disableInteraction: ${this.disableInteraction}`),console.log(`[WHEEL] Physics engine: ${!!this.physicsEngine}, Renderer: ${!!this.renderer}`),console.log("[WHEEL] Segments array:",this.segments),console.log("[WHEEL] WheelData:",this.wheelData),this.isSpinning||this.segments.length===0||this.disableInteraction||!this.physicsEngine||!this.renderer){console.log("[WHEEL] Spin blocked - checking conditions:"),console.log(`[WHEEL] - isSpinning: ${this.isSpinning}`),console.log(`[WHEEL] - segments.length: ${this.segments.length}`),console.log(`[WHEEL] - disableInteraction: ${this.disableInteraction}`),console.log(`[WHEEL] - physicsEngine: ${!!this.physicsEngine}`),console.log(`[WHEEL] - renderer: ${!!this.renderer}`),(!this.physicsEngine||!this.renderer)&&(console.log("[WHEEL] Physics engine or renderer not ready, attempting initialization..."),this.initializeWheel().then(()=>{console.log("[WHEEL] Initialization complete, retrying spin..."),setTimeout(()=>this.spin(),100)}).catch(s=>{console.error("[WHEEL] Failed to initialize wheel:",s)}));return}const t=this.wheelConfig.spinForce*.5,e=this.wheelConfig.spinForce*1.5,i=t+Math.random()*(e-t);console.log(`[WHEEL] Generated spin force: ${i} (min: ${t}, max: ${e})`),this.spinWithForce(i)}spinWithForce(t){this.isSpinning||!this.physicsEngine||this.startSpin(t)}spinWithVelocity(t){if(this.isSpinning||!this.physicsEngine)return;const e=Math.abs(t)*this.wheelConfig.spinForce;this.startSpin(e,t>0?0:Math.PI)}startSpin(t,e=0){if(console.log(`[WHEEL] Starting spin with force: ${t}, angle: ${e}`),!this.physicsEngine||!this.renderer){console.error("[WHEEL] Cannot start spin - missing physics engine or renderer"),console.log(`[WHEEL] Physics engine: ${!!this.physicsEngine}, Renderer: ${!!this.renderer}`);return}this.isSpinning=!0,this.statusMessage="Spinning...",this.spinState={isSpinning:!0,startTime:performance.now(),duration:0,initialVelocity:t,currentVelocity:t},console.log("[WHEEL] Applying spin force to wheel..."),this.physicsEngine.spinWheel(t),this.renderer.addSpinEffect(),this.dispatchEvent(new CustomEvent("wheel-spin-start",{detail:{force:t}})),console.log("[WHEEL] Starting spin monitoring..."),this.monitorSpinCompletion(),setTimeout(()=>this.startBallTracking(),100)}monitorSpinCompletion(){let t=null;const e=()=>{if(!this.physicsEngine||!this.spinState)return;const i=this.physicsEngine.isBallSettled(.005),s=this.physicsEngine.isWheelSettled(.005),n=i&&s,a=performance.now()-this.spinState.startTime,o=a>this.wheelConfig.maxSpinDuration,l=this.physicsEngine.getBallVelocity(),d=this.physicsEngine.getWheelVelocity();n&&t===null?(t=performance.now(),console.log("[WHEEL] Both ball and wheel settled! Starting 1-second wait before winner detection...")):!n&&t!==null&&(t=null,console.log("[WHEEL] Movement detected after settling, resetting wait timer..."));const h=t?performance.now()-t:0,p=h>=1e3;console.log(`[WHEEL] Monitoring: Ball settled: ${i} (vel: ${l.magnitude.toFixed(4)}), Wheel settled: ${s} (vel: ${Math.abs(d).toFixed(4)}), Time: ${(a/1e3).toFixed(1)}s, Wait: ${t?(h/1e3).toFixed(1):"N/A"}s`),n&&p||o?(console.log(`[WHEEL] Spin complete! Both settled: ${n}, Wait complete: ${p}, Timeout: ${o}`),this.completeSpin()):requestAnimationFrame(e)};requestAnimationFrame(e)}completeSpin(){if(!this.physicsEngine||!this.renderer||!this.spinState)return;const e=this.physicsEngine.getBallBody()?.position;if(!e){console.error("[WHEEL] Ball position not available for winner detection");return}console.log(`[WHEEL] Ball final position: (${e.x.toFixed(1)}, ${e.y.toFixed(1)})`);const i=this.physicsEngine.getWheelRotation();console.log(`[WHEEL] Current wheel rotation: ${(i*180/Math.PI).toFixed(1)}°`);const s=this.segments.map(p=>({...p,startAngle:p.startAngle+i,endAngle:p.endAngle+i,centerAngle:p.centerAngle+i})),n=this.physicsEngine.getNailPositions(),a=et(e.x,e.y,this.wheelConfig.centerX,this.wheelConfig.centerY,s,n,this.wheelConfig.ballRadius),o=a.segment,l=performance.now()-this.spinState.startTime;console.log("[WHEEL] Enhanced winner detection:"),console.log(`   Winner: ${o?.text||"None"}`),console.log(`   Method: ${a.method}`),console.log(`   Confidence: ${(a.confidence*100).toFixed(1)}%`),console.log("   Debug info:",a.debugInfo);const d=this.physicsEngine.getBallAngle(),h=pe(d,s);if(console.log(`[WHEEL] Simple angle-based winner: ${h?.text||"None"}`),console.log(`[WHEEL] Ball angle: ${(d*180/Math.PI).toFixed(1)}°`),this.isSpinning=!1,this.statusMessage=o?`Winner: ${o.text} (${(a.confidence*100).toFixed(0)}% confidence)`:"No winner determined",this.renderer.removeSpinEffect(),o){const p=s.find(g=>g.id===o.id||g.text===o.text&&g.color===o.color);p?(console.log(`[WHEEL] Highlighting rotated segment: ${p.text} at angles ${(p.startAngle*180/Math.PI).toFixed(1)}° to ${(p.endAngle*180/Math.PI).toFixed(1)}°`),this.renderer.highlightSegment(p)):(console.warn(`[WHEEL] Could not find rotated segment for highlighting: ${o.text}`),this.renderer.highlightSegment(o))}console.log("🎡 [WHEEL] Dispatching wheel-spin-complete event with winning segment:",o),this.dispatchEvent(new CustomEvent("wheel-spin-complete",{detail:{winningSegment:o,finalAngle:d,duration:l}})),o&&(console.log("🎡 [WHEEL] Dispatching wheel-result event"),this.dispatchEvent(new CustomEvent("wheel-result",{detail:{segment:o}}))),setTimeout(()=>{this.statusMessage=""},3e3),this.spinState=null}updateSpinState(t){this.spinState&&(this.spinState.currentVelocity=Math.max(t.ballVelocity.magnitude,Math.abs(t.wheelVelocity)),this.spinState.duration=t.timestamp-this.spinState.startTime,this.dispatchEvent(new CustomEvent("wheel-spinning",{detail:{velocity:this.spinState.currentVelocity,wheelRotation:t.wheelRotation,ballAngle:t.ballAngle}})))}updateProgressiveZoom(t){if(!this.isSpinning){this.currentZoom!==1&&(this.currentZoom=1,this.isZooming=!1,this.updateZoomTransform());return}const e=t.ballVelocity.magnitude,i=Math.abs(t.wheelVelocity),s=e+i,n=.5,a=.5,o=.05;let l=1;s<=n&&(s<=o?l=3:s<=a&&(l=1+2*((a-s)/(a-o))));const d=.08;this.currentZoom+=(l-this.currentZoom)*d,this.isZooming=this.currentZoom>1.1,this.updateZoomTransform()}updateZoomTransform(){if(!this.canvasRef.value)return;const t=this.canvasRef.value,e=this.wheelConfig.centerX,i=this.wheelConfig.centerY+this.wheelConfig.radius;t.style.transform=`scale(${this.currentZoom})`,t.style.transformOrigin=`${e}px ${i}px`,this.isZooming?t.classList.add("zooming"):t.classList.remove("zooming")}getWheelState(){return{isSpinning:this.isSpinning,segments:this.segments,config:this.wheelConfig,hasPhysicsEngine:!!this.physicsEngine,hasRenderer:!!this.renderer,spinState:this.spinState,errorMessage:this.errorMessage}}startBallTracking(){if(!this.physicsEngine){console.error("[BALL TRACKER] No physics engine available");return}const t=this.physicsEngine;console.log("[BALL TRACKER] Starting ball movement tracking...");let e=0,i=null,s=!1;const n=setInterval(()=>{const a=t.getBallBody();if(!a){console.log("[BALL TRACKER] No ball body, stopping tracking"),clearInterval(n);return}const o=a.position,l=t.getBallVelocity(),d=Math.sqrt(Math.pow(o.x-this.wheelConfig.centerX,2)+Math.pow(o.y-this.wheelConfig.centerY,2));i&&Math.sqrt(Math.pow(o.x-i.x,2)+Math.pow(o.y-i.y,2))>.1&&(s=!0),e++,console.log(`[BALL TRACKER ${e}] Pos: (${o.x.toFixed(1)}, ${o.y.toFixed(1)}) Vel: ${l.magnitude.toFixed(3)} Dist: ${d.toFixed(1)} Movement: ${s?"✅":"❌"}`),i={x:o.x,y:o.y},(e>100||!this.isSpinning&&l.magnitude<.01)&&(console.log(`[BALL TRACKER] Stopping tracking. Movement detected: ${s}`),clearInterval(n))},100)}reset(){this.physicsEngine&&this.physicsEngine.resetBall(),this.isSpinning=!1,this.statusMessage="",this.spinState=null}cleanup(){this.physicsEngine&&(this.physicsEngine.destroy(),this.physicsEngine=null),this.renderer&&(this.renderer.destroy(),this.renderer=null),this.resizeObserver&&(this.resizeObserver.disconnect(),this.resizeObserver=null)}};S.styles=O`
    :host {
      display: block;
      width: 100%;
      height: 100%;
      position: relative;
      user-select: none;
      touch-action: none;
    }

    .wheel-container {
      width: 100%;
      height: 100%;
      position: relative;
      overflow: hidden;
      border-radius: 50%;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    .wheel-canvas {
      width: 100%;
      height: 100%;
      display: block;
      cursor: pointer;
      transition: transform 0.1s ease-out;
      transform-origin: center bottom;
    }

    .wheel-canvas:active {
      cursor: grabbing;
    }

    .wheel-canvas.zooming {
      transition: transform 0.1s ease-out;
    }

    .spin-button {
      position: absolute;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      padding: 12px 24px;
      background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
      color: white;
      border: none;
      border-radius: 25px;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .spin-button:hover {
      transform: translateX(-50%) translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    }

    .spin-button:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: translateX(-50%);
    }

    .wheel-status {
      position: absolute;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 14px;
      font-weight: bold;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .wheel-status.visible {
      opacity: 1;
    }

    .error-message {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(255, 0, 0, 0.9);
      color: white;
      padding: 16px;
      border-radius: 8px;
      text-align: center;
      max-width: 300px;
    }



    .waiting {
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.8);
    }

    .waiting-message {
      text-align: center;
      color: white;
    }

    .waiting-message p {
      margin: 16px 0 0 0;
      font-size: 16px;
      font-weight: 500;
    }

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid rgba(255, 255, 255, 0.3);
      border-top: 4px solid #4ecdc4;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Winning Modal Styles - FIXED: Positioned relative to wheel */
    .winning-modal {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      opacity: 0;
      animation: modalFadeIn 0.5s ease-out forwards;
      border-radius: inherit; /* Match wheel container border radius */
    }

    .winning-modal-content {
      background: white;
      border-radius: 20px;
      padding: 40px;
      max-width: 600px;
      width: 90%;
      max-height: 80vh;
      overflow-y: auto;
      text-align: center;
      position: relative;
      transform: scale(0.7) translateY(50px);
      animation: modalSlideIn 0.6s ease-out 0.2s forwards;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    }

    .winning-header {
      margin-bottom: 30px;
    }

    .winning-title {
      font-size: 2.5rem;
      font-weight: bold;
      margin-bottom: 10px;
      background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .winning-subtitle {
      font-size: 1.2rem;
      color: #666;
      margin-bottom: 20px;
    }

    .winning-activity {
      background: var(--winning-color, #4ecdc4);
      color: white;
      padding: 30px;
      border-radius: 15px;
      margin: 20px 0;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }

    .winning-activity-name {
      font-size: 2rem;
      font-weight: bold;
      margin-bottom: 15px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .winning-activity-details {
      font-size: 1.1rem;
      line-height: 1.6;
      opacity: 0.95;
      text-align: left;
    }

    .activity-description {
      margin-bottom: 20px;
    }

    .activity-description p {
      margin: 0;
      font-size: 1rem;
      line-height: 1.5;
    }

    .activity-metadata {
      display: flex;
      flex-direction: column;
      gap: 10px;
      margin-top: 15px;
    }

    .metadata-item {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 0.95rem;
    }

    .metadata-icon {
      font-size: 1.1rem;
    }

    .metadata-label {
      font-weight: 600;
      opacity: 0.9;
    }

    .metadata-value {
      font-weight: 500;
      opacity: 0.95;
    }

    .fallback-message {
      font-style: italic;
      opacity: 0.8;
    }

    .fallback-message p {
      margin: 0;
    }

    .winning-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 20px;
      margin: 30px 0;
    }

    .winning-stat {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 10px;
      border-left: 4px solid var(--winning-color, #4ecdc4);
    }

    .winning-stat-label {
      font-size: 0.9rem;
      color: #666;
      margin-bottom: 5px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .winning-stat-value {
      font-size: 1.5rem;
      font-weight: bold;
      color: #333;
    }

    .winning-actions {
      margin-top: 30px;
      display: flex;
      gap: 15px;
      justify-content: center;
      flex-wrap: wrap;
    }

    .winning-action-btn {
      padding: 12px 24px;
      border: none;
      border-radius: 25px;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .winning-action-primary {
      background: var(--winning-color, #4ecdc4);
      color: white;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .winning-action-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    }

    .winning-action-secondary {
      background: #f8f9fa;
      color: #666;
      border: 2px solid #e9ecef;
    }

    .winning-action-secondary:hover {
      background: #e9ecef;
      color: #333;
    }

    .winning-close {
      position: absolute;
      top: 15px;
      right: 20px;
      background: none;
      border: none;
      font-size: 2rem;
      color: #999;
      cursor: pointer;
      transition: color 0.3s ease;
    }

    .winning-close:hover {
      color: #333;
    }

    .winning-confetti {
      position: absolute;
      top: -10px;
      left: 50%;
      transform: translateX(-50%);
      font-size: 3rem;
      animation: confettiFall 2s ease-out infinite;
    }

    @keyframes modalFadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }

    @keyframes modalSlideIn {
      from {
        transform: scale(0.7) translateY(50px);
        opacity: 0;
      }
      to {
        transform: scale(1) translateY(0);
        opacity: 1;
      }
    }

    @keyframes confettiFall {
      0% { transform: translateX(-50%) translateY(-20px) rotate(0deg); opacity: 1; }
      100% { transform: translateX(-50%) translateY(20px) rotate(360deg); opacity: 0; }
    }

    @media (max-width: 768px) {
      .spin-button {
        bottom: 10px;
        padding: 10px 20px;
        font-size: 14px;
      }

      .winning-modal-content {
        padding: 20px;
        margin: 20px;
      }

      .winning-title {
        font-size: 2rem;
      }

      .winning-activity-name {
        font-size: 1.5rem;
      }

      .winning-stats {
        grid-template-columns: 1fr;
      }
    }
  `;$([w({type:Object})],S.prototype,"wheelData",2);$([w({type:Object})],S.prototype,"config",2);$([w({type:Boolean})],S.prototype,"autoSpin",2);$([w({type:Number})],S.prototype,"spinDelay",2);$([w({type:Boolean})],S.prototype,"hideUI",2);$([w({type:Boolean,attribute:"disable-interaction"})],S.prototype,"disableInteraction",2);$([w({type:Boolean})],S.prototype,"invisible",2);$([c()],S.prototype,"isSpinning",2);$([c()],S.prototype,"statusMessage",2);$([c()],S.prototype,"errorMessage",2);$([c()],S.prototype,"segments",2);$([c()],S.prototype,"currentZoom",2);$([c()],S.prototype,"isZooming",2);$([c()],S.prototype,"showWinningModal",2);$([c()],S.prototype,"winningSegment",2);$([c()],S.prototype,"showWinningAnimation",2);S=$([j("game-wheel")],S);/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const Ee=(t,e,i)=>{const s=new Map;for(let n=e;n<=i;n++)s.set(t[n],n);return s},gt=re(class extends le{constructor(t){if(super(t),t.type!==oe.CHILD)throw Error("repeat() can only be used in text expressions")}dt(t,e,i){let s;i===void 0?i=e:e!==void 0&&(s=e);const n=[],a=[];let o=0;for(const l of t)n[o]=s?s(l,o):o,a[o]=i(l,o),o++;return{values:a,keys:n}}render(t,e,i){return this.dt(t,e,i).values}update(t,[e,i,s]){const n=He(t),{values:a,keys:o}=this.dt(e,i,s);if(!Array.isArray(n))return this.ut=o,a;const l=this.ut??=[],d=[];let h,p,g=0,v=n.length-1,y=0,k=a.length-1;for(;g<=v&&y<=k;)if(n[g]===null)g++;else if(n[v]===null)v--;else if(l[g]===o[y])d[y]=N(n[g],a[y]),g++,y++;else if(l[v]===o[k])d[k]=N(n[v],a[k]),v--,k--;else if(l[g]===o[k])d[k]=N(n[g],a[k]),ee(t,d[k+1],n[g]),g++,k--;else if(l[v]===o[y])d[y]=N(n[v],a[y]),ee(t,n[g],n[v]),v--,y++;else if(h===void 0&&(h=Ee(o,y,k),p=Ee(l,g,v)),h.has(l[g]))if(h.has(l[v])){const E=p.get(o[y]),P=E!==void 0?n[E]:null;if(P===null){const U=ee(t,n[g]);N(U,a[y]),d[y]=U}else d[y]=N(P,a[y]),ee(t,n[g],P),n[E]=null;y++}else ce(n[v]),v--;else ce(n[g]),g++;for(;y<=k;){const E=ee(t,d[k+1]);N(E,a[y]),d[y++]=E}for(;g<=v;){const E=n[g++];E!==null&&ce(E)}return this.ut=o,Ne(t,d),ye}});/**
 * @license
 * Copyright 2018 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const ne=re(class extends le{constructor(t){if(super(t),t.type!==oe.ATTRIBUTE||t.name!=="class"||t.strings?.length>2)throw Error("`classMap()` can only be used in the `class` attribute and must be the only part in the attribute.")}render(t){return" "+Object.keys(t).filter(e=>t[e]).join(" ")+" "}update(t,[e]){if(this.st===void 0){this.st=new Set,t.strings!==void 0&&(this.nt=new Set(t.strings.join(" ").split(/\s/).filter(s=>s!=="")));for(const s in e)e[s]&&!this.nt?.has(s)&&this.st.add(s);return this.render(e)}const i=t.element.classList;for(const s of this.st)s in e||(i.remove(s),this.st.delete(s));for(const s in e){const n=!!e[s];n===this.st.has(s)||this.nt?.has(s)||(n?(i.add(s),this.st.add(s)):(i.remove(s),this.st.delete(s)))}return ye}});var pt=Object.defineProperty,ut=Object.getOwnPropertyDescriptor,ie=(t,e,i,s)=>{for(var n=s>1?void 0:s?ut(e,i):e,a=t.length-1,o;a>=0;a--)(o=t[a])&&(n=(s?o(e,i,n):o(n))||n);return s&&n&&pt(e,i,n),n};let q=class extends R{constructor(){super(...arguments),this.showAvatar=!0,this.showTimestamp=!0,this.isTyping=!1}formatTimestamp(t){const i=new Date().getTime()-t.getTime();return i<6e4?"Just now":i<36e5?`${Math.floor(i/6e4)}m ago`:i<864e5?`${Math.floor(i/36e5)}h ago`:t.toLocaleDateString()}renderMessageContent(){let t=this.message.content;return t=this.processMarkdown(t),t=this.preserveLineBreaks(t),r`<div .innerHTML=${t}></div>`}processMarkdown(t){return t=t.replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>"),t=t.replace(/__(.*?)__/g,"<strong>$1</strong>"),t=t.replace(/\*(.*?)\*/g,"<em>$1</em>"),t=t.replace(/_(.*?)_/g,"<em>$1</em>"),t=t.replace(/`(.*?)`/g,"<code>$1</code>"),t=t.replace(/^[\s]*[-*]\s+(.+)$/gm,"<li>$1</li>"),t=t.replace(/(<li>.*<\/li>)/gs,e=>`<ul>${e}</ul>`),t}preserveLineBreaks(t){return t=t.replace(/\n\n/g,"</p><p>"),t=t.replace(/\n/g,"<br>"),!t.includes("<p>")&&!t.includes("<ul>")&&(t=`<p>${t}</p>`),t}getAvatarText(){switch(this.message.type){case"user":return"U";case"ai":return"AI";case"system":return"S";case"error":return"!";case"wheel-result":return"🎯";default:return"?"}}renderWheelResult(){const t=this.message.metadata?.wheelResult;return t?r`
      <div class="wheel-result-details">
        <div class="wheel-result-item">
          <span class="wheel-result-label">Selected:</span>
          <span class="wheel-result-value">${t.segmentText}</span>
        </div>
        <div class="wheel-result-item">
          <span class="wheel-result-label">Duration:</span>
          <span class="wheel-result-value">${(t.spinDuration/1e3).toFixed(1)}s</span>
        </div>
      </div>
    `:""}renderErrorDetails(){const t=this.message.metadata?.error;return t?r`
      <div class="error-details">
        <div class="error-code">Error ${t.code}</div>
        <div>${t.details}</div>
      </div>
    `:""}renderProcessingTime(){const t=this.message.metadata?.processingTime;return t?r`
      <div class="processing-indicator">
        ⚡ Processed in ${t}ms
      </div>
    `:""}renderTypingIndicator(){return this.isTyping?r`
      <div class="typing-indicator">
        <div class="typing-dot"></div>
        <div class="typing-dot"></div>
        <div class="typing-dot"></div>
      </div>
    `:""}render(){const t={"message-container":!0,[this.message.type]:!0},e={"message-bubble":!0,[this.message.type]:!0},i={avatar:!0,[this.message.type]:!0};return r`
      <div class=${ne(t)}>
        ${this.showAvatar?r`
          <div class=${ne(i)}>
            ${this.getAvatarText()}
          </div>
        `:""}
        
        <div class=${ne(e)}>
          <div class="message-content">
            ${this.renderMessageContent()}
            ${this.renderTypingIndicator()}
          </div>
          
          ${this.message.type==="wheel-result"?this.renderWheelResult():""}
          ${this.message.type==="error"?this.renderErrorDetails():""}
          
          <div class="message-metadata">
            ${this.renderProcessingTime()}
          </div>
        </div>
      </div>
      
      ${this.showTimestamp?r`
        <div class="timestamp">
          ${this.formatTimestamp(this.message.timestamp)}
        </div>
      `:""}
    `}};q.styles=O`
    :host {
      display: block;
      margin: 8px 0;
      animation: slideIn 0.3s ease-out;
    }

    @keyframes slideIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .message-container {
      display: flex;
      align-items: flex-end;
      gap: 8px;
    }

    .message-container.user {
      flex-direction: row-reverse;
    }

    .avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: linear-gradient(45deg, #4ecdc4, #45b7d1);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: bold;
      color: white;
      flex-shrink: 0;
    }

    .avatar.user {
      background: linear-gradient(45deg, #ff6b6b, #ffa500);
    }

    .avatar.system {
      background: linear-gradient(45deg, #96ceb4, #85c1e9);
    }

    .avatar.error {
      background: linear-gradient(45deg, #ff4757, #ff3838);
    }

    .message-bubble {
      max-width: 70%;
      padding: 12px 16px;
      border-radius: 18px;
      position: relative;
      word-wrap: break-word;
      line-height: 1.4;
    }

    .message-bubble.user {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-bottom-right-radius: 4px;
    }

    .message-bubble.ai {
      background: #f1f3f4;
      color: #333;
      border-bottom-left-radius: 4px;
      border: 1px solid #e0e0e0;
    }

    .message-bubble.system {
      background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
      color: #333;
      border-radius: 12px;
      font-style: italic;
      text-align: center;
      max-width: 90%;
      margin: 0 auto;
    }

    .message-bubble.error {
      background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
      color: #721c24;
      border: 1px solid #f5c6cb;
    }

    .message-bubble.wheel-result {
      background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
      color: #8b4513;
      border: 2px solid #ffd700;
      box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
    }

    .message-content {
      margin: 0;
    }

    .message-metadata {
      margin-top: 8px;
      padding-top: 8px;
      border-top: 1px solid rgba(255, 255, 255, 0.2);
      font-size: 12px;
      opacity: 0.8;
    }

    .message-bubble.ai .message-metadata {
      border-top-color: rgba(0, 0, 0, 0.1);
    }

    .timestamp {
      font-size: 11px;
      opacity: 0.6;
      margin-top: 4px;
      text-align: right;
    }

    .message-container.user .timestamp {
      text-align: left;
    }

    .wheel-result-details {
      display: flex;
      flex-direction: column;
      gap: 4px;
      margin-top: 8px;
      padding: 8px;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 8px;
    }

    .wheel-result-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 12px;
    }

    .wheel-result-label {
      font-weight: bold;
    }

    .wheel-result-value {
      color: #654321;
    }

    .error-details {
      margin-top: 8px;
      padding: 8px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 6px;
      font-size: 12px;
    }

    .error-code {
      font-weight: bold;
      color: #5a1a1a;
    }

    .processing-indicator {
      display: inline-flex;
      align-items: center;
      gap: 4px;
      font-size: 11px;
      opacity: 0.7;
    }

    .typing-indicator {
      display: flex;
      gap: 2px;
      margin: 4px 0;
    }

    .typing-dot {
      width: 4px;
      height: 4px;
      border-radius: 50%;
      background: currentColor;
      animation: typingPulse 1.4s ease-in-out infinite both;
    }

    .typing-dot:nth-child(1) { animation-delay: -0.32s; }
    .typing-dot:nth-child(2) { animation-delay: -0.16s; }
    .typing-dot:nth-child(3) { animation-delay: 0s; }

    @keyframes typingPulse {
      0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
      }
      40% {
        transform: scale(1);
        opacity: 1;
      }
    }

    /* Dark mode support */
    @media (prefers-color-scheme: dark) {
      .message-bubble.ai {
        background: #2d2d2d;
        color: #e0e0e0;
        border-color: #404040;
      }

      .message-bubble.system {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        color: #ecf0f1;
      }
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
      .message-bubble {
        max-width: 85%;
        padding: 10px 14px;
        font-size: 14px;
      }

      .avatar {
        width: 28px;
        height: 28px;
        font-size: 12px;
      }
    }

    @media (max-width: 480px) {
      .message-bubble {
        max-width: 90%;
        padding: 8px 12px;
        font-size: 13px;
      }

      .avatar {
        width: 24px;
        height: 24px;
        font-size: 10px;
      }
    }
  `;ie([w({type:Object})],q.prototype,"message",2);ie([w({type:Boolean})],q.prototype,"showAvatar",2);ie([w({type:Boolean})],q.prototype,"showTimestamp",2);ie([w({type:Boolean})],q.prototype,"isTyping",2);q=ie([j("message-bubble")],q);var mt=Object.defineProperty,ft=Object.getOwnPropertyDescriptor,L=(t,e,i,s)=>{for(var n=s>1?void 0:s?ft(e,i):e,a=t.length-1,o;a>=0;a--)(o=t[a])&&(n=(s?o(e,i,n):o(n))||n);return s&&n&&mt(e,i,n),n};let A=class extends R{constructor(){super(...arguments),this.messages=[],this.connectionStatus="disconnected",this.isProcessing=!1,this.isTyping=!1,this.placeholder="Type your message...",this.title="Life Coach Assistant",this.inputValue="",this.isInputFocused=!1}updated(t){t.has("messages")&&this.scrollToBottom(),t.has("isTyping")&&this.isTyping&&this.scrollToBottom()}scrollToBottom(){this.messagesContainer&&requestAnimationFrame(()=>{this.messagesContainer.scrollTop=this.messagesContainer.scrollHeight})}handleInputChange(t){const e=t.target;this.inputValue=e.value,e.style.height="auto",e.style.height=`${Math.min(e.scrollHeight,120)}px`}handleInputFocus(){this.isInputFocused=!0}handleInputBlur(){this.isInputFocused=!1}handleKeyPress(t){t.key==="Enter"&&!t.shiftKey&&(t.preventDefault(),this.sendMessage())}sendMessage(){const t=this.inputValue.trim();if(!t||this.isProcessing||this.connectionStatus!=="connected")return;const e={id:`msg-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,type:"user",content:t,timestamp:new Date};this.dispatchEvent(new CustomEvent("message-send",{detail:{message:e},bubbles:!0})),this.inputValue="",this.messageInput.style.height="auto"}addMessage(t){this.messages=[...this.messages,t]}clearMessages(){this.messages=[]}getStatusText(){switch(this.connectionStatus){case"connected":return"🟢 Connected";case"connecting":return"🟡 Connecting...";case"disconnected":return"🔴 Disconnected";default:return""}}getInputTooltip(){if(this.isProcessing)return"Please wait while your message is being processed...";switch(this.connectionStatus){case"connected":return"Type your message and press Enter to send";case"connecting":return"Connecting to server... Please wait";case"disconnected":return"Not connected to server. Please check your connection and refresh the page.";default:return"Chat unavailable"}}getPlaceholderText(){if(this.isProcessing)return"Creating your personalized wheel...";switch(this.connectionStatus){case"connected":return this.placeholder||"Tell me what you'd like to do and I'll create a personalized activity wheel for you!";case"connecting":return"Connecting to your life coach...";case"disconnected":return"Connection lost - please refresh the page";default:return"Chat unavailable"}}renderEmptyState(){return r`
      <div class="empty-state">
        <div class="empty-state-icon">💬</div>
        <div class="empty-state-text">Start a conversation</div>
        <div class="empty-state-subtext">Ask me anything about your goals and activities!</div>
      </div>
    `}renderTypingIndicator(){return this.isTyping?r`
      <div class="typing-indicator-container visible">
        <message-bubble
          .message=${{id:"typing",type:"ai",content:"AI is typing...",timestamp:new Date}}
          .isTyping=${!0}
          .showTimestamp=${!1}>
        </message-bubble>
      </div>
    `:""}render(){const t=this.messages.length>0,e=this.inputValue.trim().length>0&&this.connectionStatus==="connected";return r`
      <div class="chat-header">
        ${this.title}
        <div class="chat-status ${this.connectionStatus}">
          ${this.getStatusText()}
        </div>
      </div>

      <div class="messages-container">
        <div class="messages-list">
          ${t?gt(this.messages,i=>i.id,i=>r`
              <message-bubble .message=${i}></message-bubble>
            `):this.renderEmptyState()}
        </div>

        ${this.renderTypingIndicator()}

        <!-- Non-blocking processing indicator -->
        <div class="processing-indicator ${this.isProcessing?"visible":""}">
          <div class="dots-animation">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>

      <div class="input-container">
        <div class="input-wrapper">
          <textarea
            class="message-input"
            .value=${this.inputValue}
            ?disabled=${this.connectionStatus!=="connected"}
            placeholder=" "
            @input=${this.handleInputChange}
            @focus=${this.handleInputFocus}
            @blur=${this.handleInputBlur}
            @keypress=${this.handleKeyPress}
            rows="1"
            title=${this.getInputTooltip()}>
          </textarea>
          <div class="input-placeholder">${this.getPlaceholderText()}</div>
        </div>
        
        <button
          class="send-button"
          ?disabled=${!e||this.isProcessing}
          @click=${this.sendMessage}
          title=${this.isProcessing?"Processing...":"Send message"}>
          ${this.isProcessing?r`
            <div class="dots-animation">
              <span></span>
              <span></span>
              <span></span>
            </div>
          `:r`
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
            </svg>
          `}
        </button>
      </div>


    `}};A.styles=O`
    :host {
      display: flex;
      flex-direction: column;
      height: 100%;
      background: #f8f9fa;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .chat-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 16px;
      text-align: center;
      font-weight: bold;
      font-size: 18px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .chat-status {
      background: rgba(255, 255, 255, 0.1);
      padding: 4px 12px;
      font-size: 12px;
      text-align: center;
      opacity: 0.9;
    }

    .chat-status.connected {
      background: rgba(76, 175, 80, 0.2);
    }

    .chat-status.disconnected {
      background: rgba(244, 67, 54, 0.2);
    }

    .chat-status.connecting {
      background: rgba(255, 193, 7, 0.2);
    }

        .messages-container {
      flex: 1;
      overflow-y: auto;
      padding: 16px;
      scroll-behavior: smooth;
      max-height: 60vh; /* Prevent container from growing indefinitely */
      min-height: 300px; /* Ensure minimum usable height */
    }

    .messages-list {
      display: flex;
      flex-direction: column;
      gap: 8px;
      min-height: 100%;
      /* Ensure messages don't cause container to grow */
      overflow-wrap: break-word;
      word-wrap: break-word;
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: #666;
      text-align: center;
      padding: 32px;
    }

    .empty-state-icon {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
    }

    .empty-state-text {
      font-size: 16px;
      margin-bottom: 8px;
    }

    .empty-state-subtext {
      font-size: 14px;
      opacity: 0.7;
    }

    .input-container {
      background: white;
      border-top: 1px solid #e0e0e0;
      padding: 16px;
      display: flex;
      gap: 12px;
      align-items: flex-end;
    }

    .input-wrapper {
      flex: 1;
      position: relative;
    }

    .message-input {
      width: 100%;
      min-height: 40px;
      max-height: 120px;
      padding: 12px 16px;
      border: 2px solid #e0e0e0;
      border-radius: 20px;
      font-size: 14px;
      font-family: inherit;
      resize: none;
      outline: none;
      transition: border-color 0.2s ease;
      line-height: 1.4;
    }

    .message-input:focus {
      border-color: #667eea;
    }

    .message-input:disabled {
      background: #f5f5f5;
      color: #999;
      cursor: not-allowed;
    }

    .input-placeholder {
      position: absolute;
      top: 12px;
      left: 16px;
      color: #999;
      pointer-events: none;
      transition: all 0.2s ease;
      font-size: 14px;
    }

    .message-input:focus + .input-placeholder,
    .message-input:not(:placeholder-shown) + .input-placeholder {
      transform: translateY(-24px) scale(0.8);
      color: #667eea;
    }

    .send-button {
      width: 40px;
      height: 40px;
      border: none;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      flex-shrink: 0;
    }

    .send-button:hover:not(:disabled) {
      transform: scale(1.05);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }

    .send-button:disabled {
      background: #ccc;
      cursor: not-allowed;
      transform: none;
    }

    .send-button svg {
      width: 18px;
      height: 18px;
    }

    .send-button .dots-animation {
      justify-content: center;
    }

    .send-button .dots-animation span {
      background: white;
      width: 4px;
      height: 4px;
    }

    .typing-indicator-container {
      padding: 8px 16px;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .typing-indicator-container.visible {
      opacity: 1;
    }

    .processing-indicator {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8px 16px;
      background: rgba(102, 126, 234, 0.1);
      border-radius: 20px;
      margin: 8px 16px;
      opacity: 0;
      transition: opacity 0.3s ease;
      pointer-events: none;
    }

    .processing-indicator.visible {
      opacity: 1;
    }

    .dots-animation {
      display: flex;
      gap: 4px;
      align-items: center;
    }

    .dots-animation span {
      width: 6px;
      height: 6px;
      background: #667eea;
      border-radius: 50%;
      animation: dots-bounce 1.4s infinite ease-in-out;
    }

    .dots-animation span:nth-child(1) {
      animation-delay: -0.32s;
    }

    .dots-animation span:nth-child(2) {
      animation-delay: -0.16s;
    }

    .dots-animation span:nth-child(3) {
      animation-delay: 0s;
    }

    @keyframes dots-bounce {
      0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
      }
      40% {
        transform: scale(1);
        opacity: 1;
      }
    }

    /* Scrollbar styling */
    .messages-container::-webkit-scrollbar {
      width: 6px;
    }

    .messages-container::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    .messages-container::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }

    .messages-container::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }

    /* Dark mode support */
    @media (prefers-color-scheme: dark) {
      :host {
        background: #1a1a1a;
        color: #e0e0e0;
      }

      .input-container {
        background: #2d2d2d;
        border-top-color: #404040;
      }

      .message-input {
        background: #3d3d3d;
        color: #e0e0e0;
        border-color: #555;
      }

      .message-input:focus {
        border-color: #667eea;
      }

      .empty-state {
        color: #999;
      }

      .processing-overlay {
        background: rgba(26, 26, 26, 0.8);
      }
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
      .chat-header {
        padding: 12px;
        font-size: 16px;
      }

      .messages-container {
        padding: 12px;
      }

      .input-container {
        padding: 12px;
      }

      .message-input {
        font-size: 16px; /* Prevent zoom on iOS */
      }
    }

    @media (max-width: 480px) {
      .input-container {
        padding: 8px;
        gap: 8px;
      }

      .send-button {
        width: 36px;
        height: 36px;
      }
    }
  `;L([w({type:Array})],A.prototype,"messages",2);L([w({type:String})],A.prototype,"connectionStatus",2);L([w({type:Boolean})],A.prototype,"isProcessing",2);L([w({type:Boolean})],A.prototype,"isTyping",2);L([w({type:String})],A.prototype,"placeholder",2);L([w({type:String})],A.prototype,"title",2);L([c()],A.prototype,"inputValue",2);L([c()],A.prototype,"isInputFocused",2);L([we(".messages-container")],A.prototype,"messagesContainer",2);L([we(".message-input")],A.prototype,"messageInput",2);A=L([j("chat-interface")],A);const vt={BASE_URL:"./",DEV:!1,MODE:"production",PROD:!0,SSR:!1,VITE_API_BASE_URL:"https://goali-secure-aec2e.ondigitalocean.app",VITE_APP_MODE:"production",VITE_BUILD_MINIFY:"true",VITE_BUILD_SOURCEMAP:"false",VITE_DEBUG_ALLOW_BACKEND_URL_CHANGE:"false",VITE_DEBUG_ALLOW_LLM_CONFIG_SELECTION:"false",VITE_DEBUG_ALLOW_USER_SELECTION:"false",VITE_DEBUG_ENABLED:"false",VITE_DEBUG_MOCK_DATA_ENABLED:"false",VITE_DEBUG_SHOW_NETWORK_LOGS:"false",VITE_DEBUG_SHOW_PERFORMANCE:"false",VITE_DEBUG_SHOW_STATE_INSPECTOR:"false",VITE_LOGGING_ENABLED:"true",VITE_LOGGING_ENDPOINT:"https://goali-secure-aec2e.ondigitalocean.app/api/logs",VITE_LOGGING_LEVEL:"warn",VITE_SECURITY_ALLOWED_ORIGINS:"https://goali-secure-aec2e.ondigitalocean.app",VITE_SECURITY_REQUIRE_AUTH:"false",VITE_SECURITY_SESSION_TIMEOUT:"1800000",VITE_SECURITY_TOKEN_VALIDATION:"false",VITE_WS_URL:"wss://goali-secure-aec2e.ondigitalocean.app/ws/game/"};class B{constructor(){this.config=this.buildConfig()}static getInstance(){return B.instance||(B.instance=new B),B.instance}getConfig(){return this.config}getMode(){return this.config.mode}isDebugMode(){return this.config.mode==="debug"}isProductionMode(){return this.config.mode==="production"}updateConfig(e){if(!this.isDebugMode()){console.warn("Configuration updates are only allowed in debug mode");return}this.config={...this.config,...e},this.notifyConfigChange()}updateWebSocketUrl(e){if(!this.isDebugMode()||!this.config.debug.allowBackendUrlChange){console.warn("WebSocket URL changes are not allowed in this mode");return}this.config.websocket.url=e,this.notifyConfigChange()}buildConfig(){const e=vt,i=e.VITE_APP_MODE||"debug";return{mode:i,websocket:{url:e.VITE_WS_URL||"ws://localhost:8000/ws/game/",reconnectAttempts:5,reconnectDelay:1e3,heartbeatInterval:3e4,messageTimeout:1e4,maxQueueSize:100},wheel:{defaultSize:"80vw",spinDuration:5e3,maxSpinDuration:1e4,ballRadius:6,nailRadius:4},performance:{targetFPS:60,maxMemoryMB:50},debug:this.buildDebugConfig(e,i),security:this.buildSecurityConfig(e,i)}}buildDebugConfig(e,i){return{enabled:i==="debug"&&e.VITE_DEBUG_ENABLED==="true",showPerformanceMetrics:e.VITE_DEBUG_SHOW_PERFORMANCE==="true",showNetworkLogs:e.VITE_DEBUG_SHOW_NETWORK_LOGS==="true",showStateInspector:e.VITE_DEBUG_SHOW_STATE_INSPECTOR==="true",allowUserSelection:e.VITE_DEBUG_ALLOW_USER_SELECTION==="true",allowLLMConfigSelection:e.VITE_DEBUG_ALLOW_LLM_CONFIG_SELECTION==="true",allowBackendUrlChange:e.VITE_DEBUG_ALLOW_BACKEND_URL_CHANGE==="true",mockDataEnabled:e.VITE_DEBUG_MOCK_DATA_ENABLED==="true"}}buildSecurityConfig(e,i){return{requireAuthentication:!0,tokenValidation:e.VITE_SECURITY_TOKEN_VALIDATION==="true",allowedOrigins:e.VITE_SECURITY_ALLOWED_ORIGINS?.split(",")||[],sessionTimeout:parseInt(e.VITE_SECURITY_SESSION_TIMEOUT)||18e5}}notifyConfigChange(){window.dispatchEvent(new CustomEvent("config-changed",{detail:{config:this.config}}))}}const bt={};var yt=Object.defineProperty,wt=Object.getOwnPropertyDescriptor,b=(t,e,i,s)=>{for(var n=s>1?void 0:s?wt(e,i):e,a=t.length-1,o;a>=0;a--)(o=t[a])&&(n=(s?o(e,i,n):o(n))||n);return s&&n&&yt(e,i,n),n};let f=class extends R{constructor(){super(...arguments),this.visible=!1,this.users=[],this.llmConfigs=[],this.selectedUserId="",this.selectedLLMConfigId="",this.backendUrl="",this.connectionStatus="disconnected",this.errors=[],this.lastError="",this.isLoading=!1,this.isCreatingUser=!1,this.currentUserDetails=null,this.websocketMessages=[],this.connectionDetails={},this.performanceMetrics={},this.showRawMessages=!1,this.connectionHealth={},this.lastPingTime=0,this.responseTimeHistory=[],this.showPerformanceMetrics=!1,this.maxMessageHistory=50,this.showOnboardingDebug=!1,this.onboardingMetrics={},this.conversationState={},this.profileCompletionHistory=[],this.responseTimeThreshold=1e4,this.slowResponseAlerts=[],this.backendLogs=[],this.showBackendLogs=!1,this.maxLogHistory=100,this.isDragging=!1,this.dragOffset={x:0,y:0},this.position={x:window.innerWidth-340,y:10},this.configService=B.getInstance(),this.messageLogInterval=null,this.onboardingMonitorInterval=null,this.handleKeydown=t=>{t.ctrlKey&&t.shiftKey&&t.key==="D"&&(t.preventDefault(),this.visible=!this.visible)},this.handleConnectionChange=t=>{this.connectionStatus=t.detail.isConnected?"connected":"disconnected",this.connectionHealth={...this.connectionHealth,isConnected:t.detail.isConnected,lastConnectionChange:new Date().toISOString(),connectionQuality:this.calculateConnectionQuality()},t.detail.isConnected||(this.lastPingTime=0,this.responseTimeHistory=[])},this.handleWebSocketError=t=>{const e=`WebSocket Error: ${t.detail?.message||"Unknown error"}`;this.addError(e)},this.handleWebSocketMessage=t=>{try{const e=t.detail;if(this.logWebSocketMessage("in",e),e.type==="pong"&&this.lastPingTime>0){const i=Date.now()-this.lastPingTime;this.responseTimeHistory.push(i),this.responseTimeHistory.length>10&&(this.responseTimeHistory=this.responseTimeHistory.slice(-10)),this.lastPingTime=0}if(this.monitorOnboardingMessages(e),e.type==="error"||e.type==="debug_error_response"){const i=e.content||e.error||"Unknown error";this.addError(`Backend Error: ${i}`)}if(e.type==="debug_error_response"&&e.error_type){const i=`${e.error_type}: ${e.details||e.error||"No details available"}`;this.addError(i)}(e.type==="debug_info"||e.type==="backend_log")&&this.logBackendMessage(e)}catch(e){console.warn("Error handling WebSocket message in debug panel:",e)}},this.handleWebSocketSend=t=>{console.log("Debug Panel: WebSocket message sent",t.detail),this.logWebSocketMessage("out",t.detail)},this.handleWebSocketStateChange=t=>{this.connectionDetails={...this.connectionDetails,...t.detail,lastStateChange:new Date().toISOString()},this.requestUpdate()},this.handlePerformanceUpdate=t=>{this.performanceMetrics={...this.performanceMetrics,...t.detail,lastUpdate:new Date().toISOString()},this.requestUpdate()},this.handleMouseDown=t=>{console.log("🖱️ Debug panel drag started"),this.isDragging=!0,this.classList.add("dragging");const e=this.getBoundingClientRect();this.dragOffset={x:t.clientX-e.left,y:t.clientY-e.top},console.log("📍 Initial drag offset:",this.dragOffset),t.preventDefault()},this.handleMouseMove=t=>{if(!this.isDragging)return;const e=t.clientX-this.dragOffset.x,i=t.clientY-this.dragOffset.y,s=window.innerWidth-this.offsetWidth,n=window.innerHeight-this.offsetHeight;this.position={x:Math.max(0,Math.min(e,s)),y:Math.max(0,Math.min(i,n))},console.log("🖱️ Moving to:",this.position),this.style.left=`${this.position.x}px`,this.style.top=`${this.position.y}px`,this.style.right="auto",this.savePosition()},this.handleMouseUp=()=>{this.isDragging=!1,this.classList.remove("dragging")}}connectedCallback(){super.connectedCallback(),this.loadInitialData(),this.setupEventListeners(),this.setupDragListeners(),this.startPerformanceMonitoring(),this.startOnboardingMonitoring(),this.loadPosition()}disconnectedCallback(){super.disconnectedCallback(),this.removeEventListeners(),this.removeDragListeners(),this.stopPerformanceMonitoring(),this.stopOnboardingMonitoring()}removeDragListeners(){document.removeEventListener("mousemove",this.handleMouseMove),document.removeEventListener("mouseup",this.handleMouseUp)}async loadInitialData(){const t=this.configService.getConfig();this.backendUrl=t.websocket.url,this.selectedUserId=this.loadFromStorage("debug_selected_user_id")||bt?.VITE_USER_PROFILE_ID||"2",this.selectedLLMConfigId=this.loadFromStorage("debug_selected_llm_config_id")||"",this.backendUrl=this.loadFromStorage("debug_backend_url")||t.websocket.url,t.debug.allowUserSelection&&(await this.loadUsers(),this.selectedUserId&&await this.loadUserDetails()),t.debug.allowLLMConfigSelection&&await this.loadLLMConfigs()}async loadUsers(){this.isLoading=!0,this.clearError();try{const t=this.getApiBaseUrl();console.log("Loading users from:",`${t}/api/debug/users/`);const e=await fetch(`${t}/api/debug/users/`,{method:"GET",headers:{"Content-Type":"application/json",Accept:"application/json"},credentials:"include"});if(console.log("Users API response:",e.status,e.statusText),e.ok){const i=await e.json();this.users=i,console.log("Loaded users:",this.users.length)}else{const i=await e.text();throw new Error(`HTTP ${e.status}: ${i}`)}}catch(t){const e=t instanceof Error?t.message:"Unknown error",i=`Failed to load users: ${e}`;console.error(i,t),this.addError(i),this.requestDetailedError("load_users_failed",e)}finally{this.isLoading=!1}}async loadLLMConfigs(){this.isLoading=!0,this.clearError();try{const t=this.getApiBaseUrl();console.log("Loading LLM configs from:",`${t}/api/debug/llm-configs/`);const e=await fetch(`${t}/api/debug/llm-configs/`,{method:"GET",headers:{"Content-Type":"application/json",Accept:"application/json"},credentials:"include"});if(console.log("LLM configs API response:",e.status,e.statusText),e.ok){const i=await e.json();this.llmConfigs=i,console.log("Loaded LLM configs:",this.llmConfigs.length)}else{const i=await e.text();throw new Error(`HTTP ${e.status}: ${i}`)}}catch(t){const e=t instanceof Error?t.message:"Unknown error",i=`Failed to load LLM configs: ${e}`;console.error(i,t),this.addError(i),this.requestDetailedError("load_llm_configs_failed",e)}finally{this.isLoading=!1}}setupEventListeners(){window.addEventListener("keydown",this.handleKeydown),window.addEventListener("connection-status-changed",this.handleConnectionChange),window.addEventListener("websocket-error",this.handleWebSocketError),window.addEventListener("websocket-message",this.handleWebSocketMessage),window.addEventListener("websocket-send",this.handleWebSocketSend),window.addEventListener("websocket-state-change",this.handleWebSocketStateChange),window.addEventListener("performance-update",this.handlePerformanceUpdate)}removeEventListeners(){window.removeEventListener("keydown",this.handleKeydown),window.removeEventListener("connection-status-changed",this.handleConnectionChange),window.removeEventListener("websocket-error",this.handleWebSocketError),window.removeEventListener("websocket-message",this.handleWebSocketMessage),window.removeEventListener("websocket-send",this.handleWebSocketSend),window.removeEventListener("websocket-state-change",this.handleWebSocketStateChange),window.removeEventListener("performance-update",this.handlePerformanceUpdate)}handleClose(){this.visible=!1}handleUserChange(t){const e=t.target;this.selectedUserId=e.value,this.saveToStorage("debug_selected_user_id",this.selectedUserId),this.loadUserDetails(),this.dispatchEvent(new CustomEvent("user-changed",{detail:{userId:this.selectedUserId}}))}handleLLMConfigChange(t){const e=t.target;this.selectedLLMConfigId=e.value,this.saveToStorage("debug_selected_llm_config_id",this.selectedLLMConfigId),this.dispatchEvent(new CustomEvent("llm-config-changed",{detail:{configId:this.selectedLLMConfigId}}))}handleBackendUrlChange(t){const e=t.target;this.backendUrl=e.value,this.saveToStorage("debug_backend_url",this.backendUrl)}applyBackendUrl(){this.configService.updateWebSocketUrl(this.backendUrl),this.dispatchEvent(new CustomEvent("backend-url-changed",{detail:{url:this.backendUrl}}))}clearStorage(){localStorage.clear(),sessionStorage.clear(),location.reload()}loadMockedWheelItems(){this.dispatchEvent(new CustomEvent("load-mocked-wheel",{detail:{action:"load_mocked_items",timestamp:new Date().toISOString()},bubbles:!0,composed:!0}))}async createNewUser(){this.isCreatingUser=!0,this.clearError();try{const t=this.getApiBaseUrl();console.log("Creating new user at:",`${t}/api/debug/users/`);const e=await fetch(`${t}/api/debug/users/`,{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},credentials:"include",body:JSON.stringify({profile_type:"german_student"})});if(console.log("Create user API response:",e.status,e.statusText),e.ok){const i=await e.json();console.log("Created new user:",i),this.users=[...this.users,i.user],this.selectedUserId=i.user.id,this.saveToStorage("debug_selected_user_id",this.selectedUserId),await this.loadUserDetails(),this.dispatchEvent(new CustomEvent("user-changed",{detail:{userId:this.selectedUserId}})),console.log(`✅ Created and selected new user: ${i.user.name}`)}else{const i=await e.text();throw new Error(`HTTP ${e.status}: ${i}`)}}catch(t){console.error("Failed to create new user:",t),this.addError(`Failed to create new user: ${t instanceof Error?t.message:"Unknown error"}`)}finally{this.isCreatingUser=!1}}async loadUserDetails(){if(!this.selectedUserId){this.currentUserDetails=null;return}try{const t=this.getApiBaseUrl(),e=await fetch(`${t}/api/debug/users/${this.selectedUserId}/`,{method:"GET",headers:{"Content-Type":"application/json",Accept:"application/json"},credentials:"include"});if(e.ok){const i=await e.json();this.currentUserDetails=i,console.log("Loaded user details:",i)}else console.warn("Failed to load user details:",e.status),this.currentUserDetails=null}catch(t){console.warn("Error loading user details:",t),this.currentUserDetails=null}}getApiBaseUrl(){let e=this.configService.getConfig().websocket.url;return e.startsWith("ws://")?e=e.replace("ws://","http://"):e.startsWith("wss://")&&(e=e.replace("wss://","https://")),e=e.replace("/ws/game/",""),e}addError(t){this.errors.push(t),this.lastError=t,this.errors.length>5&&(this.errors=this.errors.slice(-5))}clearError(){this.lastError=""}clearAllErrors(){this.errors=[],this.lastError=""}requestDetailedError(t,e){try{const i={type:"debug_error_request",error_type:t,basic_error:e,timestamp:new Date().toISOString()};window.WebSocket&&window.__GOALI_WS__&&window.__GOALI_WS__.send(JSON.stringify(i))}catch(i){console.warn("Could not request detailed error via WebSocket:",i)}}saveToStorage(t,e){try{localStorage.setItem(t,e)}catch(i){console.warn(`Failed to save ${t} to localStorage:`,i)}}loadFromStorage(t){try{return localStorage.getItem(t)}catch(e){return console.warn(`Failed to load ${t} from localStorage:`,e),null}}logWebSocketMessage(t,e){const i=new Date().toISOString(),s=e?.type||"unknown";this.websocketMessages.unshift({timestamp:i,direction:t,type:s,data:e}),this.websocketMessages.length>this.maxMessageHistory&&(this.websocketMessages=this.websocketMessages.slice(0,this.maxMessageHistory)),this.requestUpdate()}startPerformanceMonitoring(){this.messageLogInterval=window.setInterval(()=>{this.updatePerformanceMetrics()},2e3)}stopPerformanceMonitoring(){this.messageLogInterval&&(clearInterval(this.messageLogInterval),this.messageLogInterval=null)}updatePerformanceMetrics(){const t=performance.now(),e=performance.memory;this.performanceMetrics={timestamp:new Date().toISOString(),uptime:Math.round(t/1e3),memory:e?{used:Math.round(e.usedJSHeapSize/1024/1024),total:Math.round(e.totalJSHeapSize/1024/1024),limit:Math.round(e.jsHeapSizeLimit/1024/1024)}:null,messageCount:this.websocketMessages.length,connectionStatus:this.connectionStatus,lastUpdate:new Date().toISOString()}}toggleRawMessages(){this.showRawMessages=!this.showRawMessages}togglePerformanceMetrics(){this.showPerformanceMetrics=!this.showPerformanceMetrics}calculateConnectionQuality(){if(this.connectionStatus!=="connected")return"poor";if(this.responseTimeHistory.length===0)return"unknown";const t=this.responseTimeHistory.reduce((e,i)=>e+i,0)/this.responseTimeHistory.length;return t<100?"excellent":t<300?"good":t<1e3?"fair":"poor"}sendPing(){if(this.connectionStatus==="connected"&&window.__GOALI_WS__){this.lastPingTime=Date.now();try{window.__GOALI_WS__.send(JSON.stringify({type:"ping",timestamp:this.lastPingTime}))}catch(t){console.warn("Failed to send ping:",t),this.lastPingTime=0}}}getConnectionQualityColor(t){switch(t){case"excellent":return"#28a745";case"good":return"#17a2b8";case"fair":return"#ffc107";case"poor":return"#dc3545";default:return"#6c757d"}}clearMessageHistory(){this.websocketMessages=[],this.requestUpdate()}startOnboardingMonitoring(){this.onboardingMonitorInterval=window.setInterval(()=>{this.checkOnboardingHealth()},2e3)}stopOnboardingMonitoring(){this.onboardingMonitorInterval&&(clearInterval(this.onboardingMonitorInterval),this.onboardingMonitorInterval=null)}checkOnboardingHealth(){const t=Date.now(),e=this.websocketMessages.filter(n=>t-new Date(n.timestamp).getTime()<3e4),i=e.filter(n=>n.direction==="out"&&n.type==="chat_message"),s=e.filter(n=>n.direction==="in"&&(n.type==="chat_message"||n.type==="processing_status"));if(i.length>s.length){const n=i[i.length-1],a=t-new Date(n.timestamp).getTime();a>this.responseTimeThreshold&&this.addSlowResponseAlert(a,"Potential hanging detected - no response to user message")}}monitorOnboardingMessages(t){if(t.type==="conversation_state_update"&&(this.conversationState={...this.conversationState,...t.updates,lastUpdate:new Date().toISOString()}),t.type==="profile_completion_update"||t.type==="debug_info"&&t.details?.profile_completion!==void 0){const e=t.details?.profile_completion||t.completion||0;this.profileCompletionHistory.push({timestamp:new Date().toISOString(),completion:e}),this.profileCompletionHistory.length>20&&(this.profileCompletionHistory=this.profileCompletionHistory.slice(-20))}if(t.type==="workflow_started"&&(this.onboardingMetrics={...this.onboardingMetrics,lastWorkflowStart:new Date().toISOString(),workflowType:t.workflow_type}),t.type==="workflow_completed"){const e=this.onboardingMetrics.lastWorkflowStart;if(e){const i=Date.now()-new Date(e).getTime();this.onboardingMetrics={...this.onboardingMetrics,lastWorkflowDuration:i,lastWorkflowCompleted:new Date().toISOString()}}}}logBackendMessage(t){const e={timestamp:new Date().toISOString(),level:t.level||"info",message:t.message||t.content||JSON.stringify(t),source:t.source||"backend"};this.backendLogs.push(e),this.backendLogs.length>this.maxLogHistory&&(this.backendLogs=this.backendLogs.slice(-this.maxLogHistory))}addSlowResponseAlert(t,e){this.slowResponseAlerts.push({timestamp:new Date().toISOString(),duration:t,message:e}),this.slowResponseAlerts.length>10&&(this.slowResponseAlerts=this.slowResponseAlerts.slice(-10))}toggleOnboardingDebug(){this.showOnboardingDebug=!this.showOnboardingDebug}toggleBackendLogs(){this.showBackendLogs=!this.showBackendLogs}setupDragListeners(){document.addEventListener("mousemove",this.handleMouseMove),document.addEventListener("mouseup",this.handleMouseUp)}loadPosition(){const t=this.loadFromStorage("debug_panel_position");if(t)try{this.position=JSON.parse(t)}catch(e){console.warn("Failed to load debug panel position:",e),this.position={x:window.innerWidth-340,y:10}}this.style.left=`${this.position.x}px`,this.style.top=`${this.position.y}px`,this.style.right="auto",this.style.bottom="auto"}savePosition(){this.saveToStorage("debug_panel_position",JSON.stringify(this.position))}render(){if(!this.visible||!this.configService.isDebugMode())return r``;const t=this.configService.getConfig();return r`
      <div class="debug-panel">
        <div class="debug-header" @mousedown=${this.handleMouseDown}>
          <span class="debug-title">🐛 DEBUG PANEL</span>
          <button class="close-btn" @click=${this.handleClose} @mousedown=${e=>e.stopPropagation()}>×</button>
        </div>

        <!-- Enhanced Connection Status -->
        <div class="debug-section">
          <div class="section-title">
            🔗 Connection Status
            ${this.connectionStatus==="connected"?r`
              <button class="btn" style="font-size: 9px; margin-left: 8px;" @click=${this.sendPing}>
                📡 Ping
              </button>
            `:""}
          </div>
          <div style="font-size: 11px;">
            <div style="margin-bottom: 4px;">
              <span class="status-indicator status-${this.connectionStatus}"></span>
              <strong>${this.connectionStatus.toUpperCase()}</strong>
              ${this.isLoading?r`<span style="margin-left: 8px;">⏳ Loading...</span>`:""}
              ${this.connectionHealth.connectionQuality?r`
                <span style="margin-left: 8px; color: ${this.getConnectionQualityColor(this.connectionHealth.connectionQuality)};">
                  ● ${this.connectionHealth.connectionQuality}
                </span>
              `:""}
            </div>
            ${this.connectionDetails.readyState!==void 0?r`
              <div><strong>Ready State:</strong> ${this.connectionDetails.readyState}</div>
            `:""}
            ${this.connectionDetails.url?r`
              <div><strong>URL:</strong> ${this.connectionDetails.url}</div>
            `:""}
            ${this.connectionDetails.lastStateChange?r`
              <div><strong>Last Change:</strong> ${new Date(this.connectionDetails.lastStateChange).toLocaleTimeString()}</div>
            `:""}
            ${this.responseTimeHistory.length>0?r`
              <div><strong>Avg Response:</strong> ${Math.round(this.responseTimeHistory.reduce((e,i)=>e+i,0)/this.responseTimeHistory.length)}ms</div>
            `:""}
            ${this.websocketMessages.length>0?r`
              <div><strong>Messages:</strong> ${this.websocketMessages.length} logged</div>
            `:""}
          </div>
        </div>

        <!-- Error Display -->
        ${this.lastError?r`
          <div class="debug-section">
            <div class="section-title" style="color: #ff6b6b;">Latest Error</div>
            <div style="background: rgba(255, 107, 107, 0.1); padding: 8px; border-radius: 4px; font-size: 10px; line-height: 1.3; color: #ff6b6b; margin-bottom: 8px;">
              ${this.lastError}
            </div>
            <button class="btn" style="font-size: 9px; padding: 3px 6px;" @click=${this.clearAllErrors}>Clear Errors</button>
          </div>
        `:""}

        <!-- All Errors (if multiple) -->
        ${this.errors.length>1?r`
          <div class="debug-section">
            <div class="section-title" style="color: #ffaa00;">Error History (${this.errors.length})</div>
            <div style="max-height: 100px; overflow-y: auto; font-size: 9px;">
              ${this.errors.map((e,i)=>r`
                <div style="padding: 2px 0; border-bottom: 1px solid rgba(255,255,255,0.1);">
                  ${i+1}. ${e}
                </div>
              `)}
            </div>
          </div>
        `:""}

        <!-- User Selection -->
        ${t.debug.allowUserSelection?r`
          <div class="debug-section">
            <div class="section-title">User Selection</div>
            <div class="form-group">
              <label class="form-label">Select User:</label>
              <select class="form-select" .value=${this.selectedUserId} @change=${this.handleUserChange} ?disabled=${this.isLoading||this.isCreatingUser}>
                <option value="">Select a user...</option>
                ${this.users.map(e=>r`
                  <option value=${e.id}>
                    ${e.name} (ID: ${e.id}) ${e.is_fake?"🤖":"👤"}
                  </option>
                `)}
              </select>
              <div style="display: flex; gap: 4px; margin-top: 4px;">
                ${this.users.length===0&&!this.isLoading?r`
                  <button class="btn" style="font-size: 10px;" @click=${this.loadUsers}>
                    🔄 Reload Users
                  </button>
                `:""}
                <button
                  class="btn"
                  style="font-size: 10px; background: #4CAF50; color: white;"
                  @click=${this.createNewUser}
                  ?disabled=${this.isCreatingUser}
                >
                  ${this.isCreatingUser?"⏳ Creating...":"👤 New German Student"}
                </button>
              </div>
            </div>

            <!-- Current User Details -->
            ${this.selectedUserId&&this.currentUserDetails?r`
              <div class="user-details" style="margin-top: 8px; padding: 8px; background: #f5f5f5; border-radius: 4px; font-size: 11px;">
                <div style="font-weight: bold; margin-bottom: 4px;">📋 Current User Details</div>
                <div><strong>Name:</strong> ${this.currentUserDetails.profile_name||"N/A"}</div>
                <div><strong>ID:</strong> ${this.currentUserDetails.id}</div>
                <div><strong>Profile Completion:</strong>
                  <span style="color: ${this.currentUserDetails.profile_completion_percentage>=50?"#28a745":this.currentUserDetails.profile_completion_percentage>=25?"#ffc107":"#dc3545"};">
                    ${this.currentUserDetails.profile_completion_percentage||0}%
                  </span>
                </div>
                <div><strong>Is Real User:</strong> ${this.currentUserDetails.is_real?"👤 Yes":"🤖 Test"}</div>
                ${this.currentUserDetails.demographics?r`
                  <div style="margin-top: 4px; border-top: 1px solid #ddd; padding-top: 4px;">
                    <div style="font-weight: bold; margin-bottom: 2px;">👤 Demographics</div>
                    <div><strong>Age:</strong> ${this.currentUserDetails.demographics.age||"N/A"}</div>
                    <div><strong>Gender:</strong> ${this.currentUserDetails.demographics.gender||"N/A"}</div>
                    <div><strong>Location:</strong> ${this.currentUserDetails.demographics.location||"N/A"}</div>
                    <div><strong>Occupation:</strong> ${this.currentUserDetails.demographics.occupation||"N/A"}</div>
                    ${this.currentUserDetails.demographics.language?r`
                      <div><strong>Language:</strong> ${this.currentUserDetails.demographics.language}</div>
                    `:""}
                  </div>
                `:r`
                  <div style="margin-top: 4px; color: #dc3545;">⚠️ No demographics data</div>
                `}
                ${this.currentUserDetails.preferences&&this.currentUserDetails.preferences.length>0?r`
                  <div style="margin-top: 4px; border-top: 1px solid #ddd; padding-top: 4px;">
                    <div style="font-weight: bold; margin-bottom: 2px;">⚙️ Preferences (${this.currentUserDetails.preferences.length})</div>
                    ${this.currentUserDetails.preferences.slice(0,3).map(e=>r`
                      <div style="font-size: 10px;">• ${e.preference_type}: ${e.content}</div>
                    `)}
                    ${this.currentUserDetails.preferences.length>3?r`
                      <div style="font-size: 10px; color: #666;">... and ${this.currentUserDetails.preferences.length-3} more</div>
                    `:""}
                  </div>
                `:r`
                  <div style="margin-top: 4px; color: #ffc107;">⚠️ No preferences set</div>
                `}
                ${this.currentUserDetails.goals&&this.currentUserDetails.goals.length>0?r`
                  <div style="margin-top: 4px; border-top: 1px solid #ddd; padding-top: 4px;">
                    <div style="font-weight: bold; margin-bottom: 2px;">🎯 Goals (${this.currentUserDetails.goals.length})</div>
                    ${this.currentUserDetails.goals.slice(0,2).map(e=>r`
                      <div style="font-size: 10px;">• ${e.goal_type}: ${e.content}</div>
                    `)}
                  </div>
                `:r`
                  <div style="margin-top: 4px; color: #ffc107;">⚠️ No goals set</div>
                `}
              </div>
            `:this.selectedUserId?r`
              <div style="margin-top: 8px; padding: 8px; background: #fff3cd; border-radius: 4px; font-size: 11px;">
                ⏳ Loading user details...
              </div>
            `:""}
          </div>
        `:""}

        <!-- LLM Configuration -->
        ${t.debug.allowLLMConfigSelection?r`
          <div class="debug-section">
            <div class="section-title">LLM Configuration</div>
            <div class="form-group">
              <label class="form-label">Select LLM Config:</label>
              <select class="form-select" .value=${this.selectedLLMConfigId} @change=${this.handleLLMConfigChange} ?disabled=${this.isLoading}>
                <option value="">Select config...</option>
                ${this.llmConfigs.map(e=>r`
                  <option value=${e.id}>
                    ${e.name} (${e.model_name}) ${e.is_default?"⭐":""}
                  </option>
                `)}
              </select>
              ${this.llmConfigs.length===0&&!this.isLoading?r`
                <button class="btn" style="margin-top: 4px; font-size: 10px;" @click=${this.loadLLMConfigs}>
                  🔄 Reload LLM Configs
                </button>
              `:""}
            </div>
          </div>
        `:""}

        <!-- Backend URL -->
        ${t.debug.allowBackendUrlChange?r`
          <div class="debug-section">
            <div class="section-title">Backend Configuration</div>
            <div class="form-group">
              <label class="form-label">WebSocket URL:</label>
              <input 
                type="text" 
                class="form-input" 
                .value=${this.backendUrl}
                @input=${this.handleBackendUrlChange}
                placeholder="ws://localhost:8000/ws/game/"
              />
              <button class="btn" @click=${this.applyBackendUrl}>Apply</button>
            </div>
          </div>
        `:""}

        <!-- WebSocket Message Log -->
        <div class="debug-section">
          <div class="section-title">
            📡 WebSocket Messages
            <button class="btn" style="font-size: 9px; margin-left: 8px;" @click=${this.toggleRawMessages}>
              ${this.showRawMessages?"Hide":"Show"} Raw
            </button>
            <button class="btn" style="font-size: 9px; margin-left: 4px;" @click=${this.clearMessageHistory}>
              Clear
            </button>
          </div>
          ${this.showRawMessages&&this.websocketMessages.length>0?r`
            <div style="max-height: 200px; overflow-y: auto; font-size: 9px; background: #f8f9fa; border-radius: 4px; padding: 8px;">
              ${this.websocketMessages.map(e=>r`
                <div style="margin-bottom: 8px; padding: 4px; border-left: 3px solid ${e.direction==="in"?"#28a745":"#007bff"}; background: white;">
                  <div style="font-weight: bold; color: ${e.direction==="in"?"#28a745":"#007bff"};">
                    ${e.direction==="in"?"⬇️ IN":"⬆️ OUT"} ${e.type}
                    <span style="color: #666; font-weight: normal;">${new Date(e.timestamp).toLocaleTimeString()}</span>
                  </div>
                  <div style="margin-top: 2px; font-family: monospace; white-space: pre-wrap; max-height: 100px; overflow-y: auto;">
                    ${JSON.stringify(e.data,null,2)}
                  </div>
                </div>
              `)}
            </div>
          `:r`
            <div style="font-size: 11px; color: #666;">
              ${this.websocketMessages.length===0?"No messages logged yet":`${this.websocketMessages.length} messages logged (click Show Raw to view)`}
            </div>
          `}
        </div>

        <!-- Onboarding Debug -->
        <div class="debug-section">
          <div class="section-title">
            🎯 Onboarding Monitor
            <button class="btn" style="font-size: 9px; margin-left: 8px;" @click=${this.toggleOnboardingDebug}>
              ${this.showOnboardingDebug?"Hide":"Show"} Details
            </button>
          </div>
          ${this.showOnboardingDebug?r`
            <div style="font-size: 11px;">
              <!-- Conversation State -->
              ${this.conversationState.phase?r`
                <div style="margin-bottom: 8px; padding: 6px; background: rgba(0,255,0,0.1); border-radius: 4px;">
                  <div><strong>Phase:</strong> ${this.conversationState.phase}</div>
                  <div><strong>Awaiting:</strong> ${this.conversationState.awaiting_response_type||"None"}</div>
                  ${this.conversationState.lastUpdate?r`
                    <div><strong>Last Update:</strong> ${new Date(this.conversationState.lastUpdate).toLocaleTimeString()}</div>
                  `:""}
                </div>
              `:""}

              <!-- Profile Completion History -->
              ${this.profileCompletionHistory.length>0?r`
                <div style="margin-bottom: 8px;">
                  <div style="font-weight: bold; margin-bottom: 4px;">📊 Profile Completion</div>
                  <div style="max-height: 80px; overflow-y: auto;">
                    ${this.profileCompletionHistory.slice(-5).map(e=>r`
                      <div style="font-size: 10px; padding: 2px 0;">
                        ${new Date(e.timestamp).toLocaleTimeString()}: ${(e.completion*100).toFixed(1)}%
                      </div>
                    `)}
                  </div>
                </div>
              `:""}

              <!-- Slow Response Alerts -->
              ${this.slowResponseAlerts.length>0?r`
                <div style="margin-bottom: 8px;">
                  <div style="font-weight: bold; margin-bottom: 4px; color: #ff6b6b;">⚠️ Slow Response Alerts</div>
                  <div style="max-height: 80px; overflow-y: auto;">
                    ${this.slowResponseAlerts.slice(-3).map(e=>r`
                      <div style="font-size: 10px; padding: 2px 0; color: #ff6b6b;">
                        ${new Date(e.timestamp).toLocaleTimeString()}: ${(e.duration/1e3).toFixed(1)}s - ${e.message}
                      </div>
                    `)}
                  </div>
                </div>
              `:""}

              <!-- Workflow Metrics -->
              ${this.onboardingMetrics.workflowType?r`
                <div style="margin-bottom: 8px;">
                  <div style="font-weight: bold; margin-bottom: 4px;">🔄 Workflow Status</div>
                  <div><strong>Type:</strong> ${this.onboardingMetrics.workflowType}</div>
                  ${this.onboardingMetrics.lastWorkflowDuration?r`
                    <div><strong>Duration:</strong> ${(this.onboardingMetrics.lastWorkflowDuration/1e3).toFixed(1)}s</div>
                  `:""}
                  ${this.onboardingMetrics.lastWorkflowCompleted?r`
                    <div><strong>Completed:</strong> ${new Date(this.onboardingMetrics.lastWorkflowCompleted).toLocaleTimeString()}</div>
                  `:""}
                </div>
              `:""}
            </div>
          `:r`
            <div style="font-size: 11px; color: #666;">
              Monitoring onboarding flow for hanging issues...
              ${this.slowResponseAlerts.length>0?r`
                <span style="color: #ff6b6b; margin-left: 8px;">⚠️ ${this.slowResponseAlerts.length} alerts</span>
              `:""}
            </div>
          `}
        </div>

        <!-- Backend Logs -->
        <div class="debug-section">
          <div class="section-title">
            📋 Backend Logs
            <button class="btn" style="font-size: 9px; margin-left: 8px;" @click=${this.toggleBackendLogs}>
              ${this.showBackendLogs?"Hide":"Show"} Logs
            </button>
          </div>
          ${this.showBackendLogs&&this.backendLogs.length>0?r`
            <div style="max-height: 150px; overflow-y: auto; font-size: 9px; background: #f8f9fa; border-radius: 4px; padding: 8px;">
              ${this.backendLogs.slice(-20).map(e=>r`
                <div style="margin-bottom: 4px; padding: 2px; border-left: 3px solid ${e.level==="error"?"#dc3545":e.level==="warning"?"#ffc107":"#28a745"};">
                  <span style="color: #666;">${new Date(e.timestamp).toLocaleTimeString()}</span>
                  <span style="color: ${e.level==="error"?"#dc3545":e.level==="warning"?"#ffc107":"#666"}; font-weight: bold; margin-left: 4px;">[${e.level.toUpperCase()}]</span>
                  <span style="margin-left: 4px;">${e.message}</span>
                </div>
              `)}
            </div>
          `:r`
            <div style="font-size: 11px; color: #666;">
              ${this.backendLogs.length===0?"No backend logs received":`${this.backendLogs.length} logs (click Show Logs to view)`}
            </div>
          `}
        </div>

        <!-- Performance Metrics -->
        <div class="debug-section">
          <div class="section-title">
            ⚡ Performance
            <button class="btn" style="font-size: 9px; margin-left: 8px;" @click=${this.togglePerformanceMetrics}>
              ${this.showPerformanceMetrics?"Hide":"Show"} Details
            </button>
          </div>
          ${this.showPerformanceMetrics&&this.performanceMetrics.timestamp?r`
            <div style="font-size: 11px;">
              <div><strong>Uptime:</strong> ${this.performanceMetrics.uptime}s</div>
              ${this.performanceMetrics.memory?r`
                <div><strong>Memory:</strong> ${this.performanceMetrics.memory.used}MB / ${this.performanceMetrics.memory.total}MB</div>
                <div><strong>Memory Limit:</strong> ${this.performanceMetrics.memory.limit}MB</div>
              `:""}
              <div><strong>Messages Logged:</strong> ${this.performanceMetrics.messageCount}</div>
              <div><strong>Last Update:</strong> ${new Date(this.performanceMetrics.lastUpdate).toLocaleTimeString()}</div>
            </div>
          `:r`
            <div style="font-size: 11px; color: #666;">
              Click "Show Details" to view performance metrics
            </div>
          `}
        </div>

        <!-- Actions -->
        <div class="debug-section">
          <div class="section-title">🔧 Actions</div>
          <div style="display: flex; flex-wrap: wrap; gap: 4px;">
            <button class="btn" style="background: #28a745;" @click=${this.loadMockedWheelItems}>🎡 Load Mocked Items</button>
            <button class="btn btn-danger" @click=${this.clearStorage}>Clear Storage</button>
            <button class="btn" @click=${this.clearMessageHistory}>Clear Messages</button>
            <button class="btn" @click=${this.clearAllErrors}>Clear Errors</button>
          </div>
        </div>
      </div>
    `}};f.styles=O`
    :host {
      position: fixed;
      /* FIXED: Remove initial positioning to allow drag positioning */
      width: 320px;
      background: rgba(0, 0, 0, 0.9);
      color: white;
      border-radius: 8px;
      padding: 16px;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 12px;
      z-index: 10000;
      max-height: 80vh;
      overflow-y: auto;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      user-select: none;
    }

    :host(.dragging) {
      opacity: 0.8;
      transform: scale(0.98);
      transition: none;
    }

    .debug-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
      cursor: move; /* Make header draggable */
    }

    .debug-title {
      font-weight: bold;
      color: #00ff00;
    }

    .close-btn {
      background: none;
      border: none;
      color: white;
      cursor: pointer;
      font-size: 16px;
      padding: 4px;
    }

    .debug-section {
      margin-bottom: 16px;
    }

    .section-title {
      font-weight: bold;
      color: #ffff00;
      margin-bottom: 8px;
      font-size: 11px;
      text-transform: uppercase;
    }

    .form-group {
      margin-bottom: 12px;
    }

    .form-label {
      display: block;
      margin-bottom: 4px;
      color: #ccc;
      font-size: 10px;
    }

    .form-input, .form-select {
      width: 100%;
      padding: 6px;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 4px;
      color: white;
      font-size: 11px;
    }

    .form-input:focus, .form-select:focus {
      outline: none;
      border-color: #00ff00;
    }

    .checkbox-group {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;
    }

    .checkbox {
      width: 14px;
      height: 14px;
    }

    .btn {
      background: #007acc;
      border: none;
      color: white;
      padding: 6px 12px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 10px;
      margin-right: 8px;
      margin-bottom: 4px;
    }

    .btn:hover {
      background: #005a9e;
    }

    .btn-danger {
      background: #dc3545;
    }

    .btn-danger:hover {
      background: #c82333;
    }

    .status-indicator {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 8px;
    }

    .status-connected {
      background: #00ff00;
    }

    .status-disconnected {
      background: #ff0000;
    }

    .status-demo {
      background: #ffaa00;
    }

    .metrics {
      font-size: 10px;
      line-height: 1.4;
    }

    .hidden {
      display: none;
    }
  `;b([w({type:Boolean})],f.prototype,"visible",2);b([c()],f.prototype,"users",2);b([c()],f.prototype,"llmConfigs",2);b([c()],f.prototype,"selectedUserId",2);b([c()],f.prototype,"selectedLLMConfigId",2);b([c()],f.prototype,"backendUrl",2);b([c()],f.prototype,"connectionStatus",2);b([c()],f.prototype,"errors",2);b([c()],f.prototype,"lastError",2);b([c()],f.prototype,"isLoading",2);b([c()],f.prototype,"isCreatingUser",2);b([c()],f.prototype,"currentUserDetails",2);b([c()],f.prototype,"websocketMessages",2);b([c()],f.prototype,"connectionDetails",2);b([c()],f.prototype,"performanceMetrics",2);b([c()],f.prototype,"showRawMessages",2);b([c()],f.prototype,"connectionHealth",2);b([c()],f.prototype,"lastPingTime",2);b([c()],f.prototype,"responseTimeHistory",2);b([c()],f.prototype,"showPerformanceMetrics",2);b([c()],f.prototype,"maxMessageHistory",2);b([c()],f.prototype,"showOnboardingDebug",2);b([c()],f.prototype,"onboardingMetrics",2);b([c()],f.prototype,"conversationState",2);b([c()],f.prototype,"profileCompletionHistory",2);b([c()],f.prototype,"responseTimeThreshold",2);b([c()],f.prototype,"slowResponseAlerts",2);b([c()],f.prototype,"backendLogs",2);b([c()],f.prototype,"showBackendLogs",2);b([c()],f.prototype,"maxLogHistory",2);b([c()],f.prototype,"isDragging",2);b([c()],f.prototype,"dragOffset",2);b([c()],f.prototype,"position",2);f=b([j("debug-panel")],f);class F{constructor(){this.token=null,this.user=null,this.configService=B.getInstance(),this.loadStoredAuth()}static getInstance(){return F.instance||(F.instance=new F),F.instance}isAuthenticated(){return this.configService.getConfig().security.requireAuthentication?this.token!==null&&this.isTokenValid():!0}getCurrentUser(){return this.user}getToken(){return this.token?.token||null}async authenticate(e,i){try{const s=this.configService.getConfig(),n=await fetch(`${s.websocket.url.replace("ws://","http://").replace("wss://","https://").replace("/ws/game/","")}/api/auth/login/`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:e,password:i})});if(n.ok){const a=await n.json();return this.token={token:a.token,userId:a.user.id,expiresAt:Date.now()+a.expires_in*1e3,permissions:a.permissions||[]},this.user=a.user,this.storeAuth(),this.notifyAuthChange(),!0}return!1}catch(s){return console.error("Authentication failed:",s),!1}}async authenticateWithToken(e){try{const i=this.configService.getConfig(),s=await fetch(`${i.websocket.url.replace("ws://","http://").replace("wss://","https://").replace("/ws/game/","")}/api/auth/verify/`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${e}`}});if(s.ok){const n=await s.json();return this.token={token:e,userId:n.user.id,expiresAt:Date.now()+n.expires_in*1e3,permissions:n.permissions||[]},this.user=n.user,this.storeAuth(),this.notifyAuthChange(),!0}return!1}catch(i){return console.error("Token authentication failed:",i),!1}}async logout(){try{if(this.token){const e=this.configService.getConfig();await fetch(`${e.websocket.url.replace("ws://","http://").replace("wss://","https://").replace("/ws/game/","")}/api/auth/logout/`,{method:"POST",headers:{Authorization:`Bearer ${this.token.token}`}})}}catch(e){console.error("Logout request failed:",e)}finally{this.token=null,this.user=null,this.clearStoredAuth(),this.notifyAuthChange()}}async refreshToken(){if(!this.token)return!1;try{const e=this.configService.getConfig(),i=await fetch(`${e.websocket.url.replace("ws://","http://").replace("wss://","https://").replace("/ws/game/","")}/api/auth/refresh/`,{method:"POST",headers:{Authorization:`Bearer ${this.token.token}`}});if(i.ok){const s=await i.json();return this.token={...this.token,token:s.token,expiresAt:Date.now()+s.expires_in*1e3},this.storeAuth(),!0}return!1}catch(e){return console.error("Token refresh failed:",e),!1}}isTokenValid(){if(!this.token)return!1;const e=5*60*1e3;return Date.now()<this.token.expiresAt-e}loadStoredAuth(){try{const e=localStorage.getItem("goali_auth_token"),i=localStorage.getItem("goali_auth_user");e&&i&&(this.token=JSON.parse(e),this.user=JSON.parse(i),this.isTokenValid()||this.clearStoredAuth())}catch(e){console.error("Failed to load stored auth:",e),this.clearStoredAuth()}}storeAuth(){this.token&&this.user&&(localStorage.setItem("goali_auth_token",JSON.stringify(this.token)),localStorage.setItem("goali_auth_user",JSON.stringify(this.user)))}clearStoredAuth(){localStorage.removeItem("goali_auth_token"),localStorage.removeItem("goali_auth_user"),this.token=null,this.user=null}notifyAuthChange(){window.dispatchEvent(new CustomEvent("auth-changed",{detail:{isAuthenticated:this.isAuthenticated(),user:this.user}}))}setupAutoRefresh(){setInterval(async()=>{this.isAuthenticated()&&this.token&&Date.now()>this.token.expiresAt-6e5&&await this.refreshToken()},6e4)}}var xt=Object.defineProperty,kt=Object.getOwnPropertyDescriptor,T=(t,e,i,s)=>{for(var n=s>1?void 0:s?kt(e,i):e,a=t.length-1,o;a>=0;a--)(o=t[a])&&(n=(s?o(e,i,n):o(n))||n);return s&&n&&xt(e,i,n),n};let _=class extends R{constructor(){super(...arguments),this.username="",this.password="",this.isLoading=!1,this.error="",this.showBetaSignup=!1,this.betaEmail="",this.betaMessage="",this.betaLoading=!1,this.betaError="",this.betaSuccess=!1,this.hideDemoMode=!1,this.authService=F.getInstance()}handleUsernameChange(t){const e=t.target;this.username=e.value,this.error=""}handlePasswordChange(t){const e=t.target;this.password=e.value,this.error=""}async handleSubmit(t){if(t.preventDefault(),!this.username.trim()||!this.password.trim()){this.error="Please enter both username and password";return}this.isLoading=!0,this.error="";try{await this.authService.authenticate(this.username.trim(),this.password)?this.dispatchEvent(new CustomEvent("login-success",{bubbles:!0,composed:!0})):this.error="Invalid username or password. Please try again."}catch(e){console.error("Login error:",e),this.error="Login failed. Please check your connection and try again."}finally{this.isLoading=!1}}handleDemoMode(){this.dispatchEvent(new CustomEvent("demo-mode-requested",{bubbles:!0,composed:!0}))}handleKeyPress(t){t.key==="Enter"&&!this.isLoading&&this.handleSubmit(t)}handleBetaEmailChange(t){const e=t.target;this.betaEmail=e.value,this.betaError=""}handleBetaMessageChange(t){const e=t.target;this.betaMessage=e.value}showBetaSignupForm(){this.showBetaSignup=!0,this.betaError="",this.betaSuccess=!1}hideBetaSignupForm(){this.showBetaSignup=!1,this.betaEmail="",this.betaMessage="",this.betaError="",this.betaSuccess=!1}async handleBetaSubmit(t){if(t.preventDefault(),!this.betaEmail.trim()){this.betaError="Please enter your email address";return}if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(this.betaEmail.trim())){this.betaError="Please enter a valid email address";return}this.betaLoading=!0,this.betaError="";try{const i=await fetch("/api/auth/beta-signup/",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:this.betaEmail.trim(),message:this.betaMessage.trim()})}),s=await i.json();i.ok&&s.success?(this.betaSuccess=!0,this.betaEmail="",this.betaMessage=""):this.betaError=s.error||"Failed to submit beta signup. Please try again."}catch(i){console.error("Beta signup error:",i),this.betaError="Network error. Please check your connection and try again."}finally{this.betaLoading=!1}}render(){return r`
      <div class="login-container">
        <div class="login-header">
          <div class="logo">🎯</div>
          <h1 class="title">Welcome to Goali</h1>
          <p class="subtitle">Sign in to access your personalized activity wheel</p>
        </div>

        <form @submit=${this.handleSubmit}>
          ${this.error?r`
            <div class="error-message">
              ${this.error}
            </div>
          `:""}

          <div class="form-group">
            <label class="form-label" for="username">Username</label>
            <input
              type="text"
              id="username"
              class="form-input ${this.error?"error":""}"
              .value=${this.username}
              @input=${this.handleUsernameChange}
              @keypress=${this.handleKeyPress}
              ?disabled=${this.isLoading}
              autocomplete="username"
              required
            />
          </div>

          <div class="form-group">
            <label class="form-label" for="password">Password</label>
            <input
              type="password"
              id="password"
              class="form-input ${this.error?"error":""}"
              .value=${this.password}
              @input=${this.handlePasswordChange}
              @keypress=${this.handleKeyPress}
              ?disabled=${this.isLoading}
              autocomplete="current-password"
              required
            />
          </div>

          <button
            type="submit"
            class="login-button"
            ?disabled=${this.isLoading}
          >
            ${this.isLoading?r`
              <span class="loading-spinner"></span>
              Signing in...
            `:"Sign In"}
          </button>
        </form>

        ${this.hideDemoMode?"":r`
          <div class="demo-notice">
            <p>Want to try Goali without signing up?</p>
            <button class="demo-button" @click=${this.handleDemoMode}>
              Try Demo Mode
            </button>
          </div>
        `}

        <!-- Beta Signup Section -->
        <div class="beta-notice">
          <h3>🚀 Closed Beta</h3>
          <p>Goali is currently in closed beta. We're not accepting new registrations at this time, but we'd love to hear from you!</p>

          ${this.betaSuccess?r`
            <div class="beta-success">
              <h3>✅ Thank You!</h3>
              <p>We've received your interest and will contact you when beta access becomes available.</p>
            </div>
          `:r`
            ${this.showBetaSignup?r`
              <form class="beta-form" @submit=${this.handleBetaSubmit}>
                ${this.betaError?r`
                  <div class="error-message">
                    ${this.betaError}
                  </div>
                `:""}

                <div class="form-group">
                  <label class="form-label" for="beta-email">Email Address *</label>
                  <input
                    type="email"
                    id="beta-email"
                    class="form-input"
                    .value=${this.betaEmail}
                    @input=${this.handleBetaEmailChange}
                    ?disabled=${this.betaLoading}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>

                <div class="form-group">
                  <label class="form-label" for="beta-message">Tell us about your interest (optional)</label>
                  <textarea
                    id="beta-message"
                    class="form-textarea"
                    .value=${this.betaMessage}
                    @input=${this.handleBetaMessageChange}
                    ?disabled=${this.betaLoading}
                    placeholder="What interests you about Goali? What would you like to achieve?"
                    rows="3"
                  ></textarea>
                </div>

                <div class="beta-actions">
                  <button
                    type="button"
                    class="beta-cancel-button"
                    @click=${this.hideBetaSignupForm}
                    ?disabled=${this.betaLoading}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    class="beta-submit-button"
                    ?disabled=${this.betaLoading}
                  >
                    ${this.betaLoading?r`
                      <span class="loading-spinner"></span>
                      Submitting...
                    `:"Join Waitlist"}
                  </button>
                </div>
              </form>
            `:r`
              <button class="beta-signup-button" @click=${this.showBetaSignupForm}>
                Join the Waitlist
              </button>
            `}
          `}
        </div>
      </div>
    `}};_.styles=O`
    :host {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      background: var(--gradient-primary);
      padding: var(--spacing-4);
    }

    .login-container {
      background: rgba(255, 255, 255, 0.95);
      border-radius: var(--radius-2xl);
      padding: var(--spacing-8);
      box-shadow: var(--shadow-xl);
      backdrop-filter: blur(10px);
      width: 100%;
      max-width: 400px;
    }

    .login-header {
      text-align: center;
      margin-bottom: var(--spacing-8);
    }

    .logo {
      font-size: 3rem;
      margin-bottom: var(--spacing-4);
    }

    .title {
      font-size: var(--font-size-2xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-gray-900);
      margin-bottom: var(--spacing-2);
    }

    .subtitle {
      color: var(--color-gray-600);
      font-size: var(--font-size-sm);
    }

    .form-group {
      margin-bottom: var(--spacing-6);
    }

    .form-label {
      display: block;
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-700);
      margin-bottom: var(--spacing-2);
      font-size: var(--font-size-sm);
    }

    .form-input {
      width: 100%;
      padding: var(--spacing-3);
      border: 2px solid var(--color-gray-300);
      border-radius: var(--radius-lg);
      font-size: var(--font-size-base);
      transition: border-color var(--transition-fast);
      box-sizing: border-box;
    }

    .form-input:focus {
      outline: none;
      border-color: var(--color-primary);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .form-input.error {
      border-color: var(--color-error);
    }

    .login-button {
      width: 100%;
      background: var(--color-primary);
      color: white;
      border: none;
      padding: var(--spacing-4);
      border-radius: var(--radius-lg);
      font-size: var(--font-size-base);
      font-weight: var(--font-weight-medium);
      cursor: pointer;
      transition: background-color var(--transition-fast);
      margin-bottom: var(--spacing-4);
    }

    .login-button:hover:not(:disabled) {
      background: var(--color-primary-dark);
    }

    .login-button:disabled {
      background: var(--color-gray-400);
      cursor: not-allowed;
    }

    .error-message {
      background: var(--color-error-light);
      color: var(--color-error-dark);
      padding: var(--spacing-3);
      border-radius: var(--radius-lg);
      font-size: var(--font-size-sm);
      margin-bottom: var(--spacing-4);
      border: 1px solid var(--color-error);
    }

    .loading-spinner {
      display: inline-block;
      width: 16px;
      height: 16px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-top: 2px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: var(--spacing-2);
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .demo-notice {
      background: var(--color-warning-light);
      color: var(--color-warning-dark);
      padding: var(--spacing-3);
      border-radius: var(--radius-lg);
      font-size: var(--font-size-sm);
      text-align: center;
      margin-top: var(--spacing-4);
      border: 1px solid var(--color-warning);
    }

    .demo-button {
      background: var(--color-warning);
      color: white;
      border: none;
      padding: var(--spacing-2) var(--spacing-4);
      border-radius: var(--radius-md);
      font-size: var(--font-size-sm);
      cursor: pointer;
      margin-top: var(--spacing-2);
    }

    .demo-button:hover {
      background: var(--color-warning-dark);
    }

    .beta-notice {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: var(--spacing-4);
      border-radius: var(--radius-lg);
      text-align: center;
      margin-top: var(--spacing-4);
    }

    .beta-notice h3 {
      margin: 0 0 var(--spacing-2) 0;
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-bold);
    }

    .beta-notice p {
      margin: 0 0 var(--spacing-3) 0;
      opacity: 0.9;
      line-height: 1.5;
    }

    .beta-signup-button {
      background: rgba(255, 255, 255, 0.2);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.3);
      padding: var(--spacing-2) var(--spacing-4);
      border-radius: var(--radius-md);
      font-size: var(--font-size-sm);
      cursor: pointer;
      transition: all 0.2s ease;
      margin-top: var(--spacing-2);
    }

    .beta-signup-button:hover {
      background: rgba(255, 255, 255, 0.3);
      border-color: rgba(255, 255, 255, 0.5);
    }

    .beta-form {
      margin-top: var(--spacing-4);
      padding: var(--spacing-4);
      background: rgba(255, 255, 255, 0.1);
      border-radius: var(--radius-lg);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .beta-form .form-group {
      margin-bottom: var(--spacing-4);
    }

    .beta-form .form-label {
      color: white;
      font-weight: var(--font-weight-medium);
    }

    .beta-form .form-input,
    .beta-form .form-textarea {
      background: rgba(255, 255, 255, 0.9);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: var(--color-gray-900);
    }

    .beta-form .form-input:focus,
    .beta-form .form-textarea:focus {
      background: white;
      border-color: var(--color-primary);
    }

    .form-textarea {
      width: 100%;
      padding: var(--spacing-3);
      border: 2px solid var(--color-gray-300);
      border-radius: var(--radius-lg);
      font-size: var(--font-size-base);
      transition: border-color var(--transition-fast);
      box-sizing: border-box;
      resize: vertical;
      min-height: 80px;
      font-family: inherit;
    }

    .beta-actions {
      display: flex;
      gap: var(--spacing-3);
      justify-content: flex-end;
    }

    .beta-submit-button {
      background: rgba(255, 255, 255, 0.9);
      color: #667eea;
      border: none;
      padding: var(--spacing-3) var(--spacing-4);
      border-radius: var(--radius-lg);
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .beta-submit-button:hover:not(:disabled) {
      background: white;
      transform: translateY(-1px);
    }

    .beta-submit-button:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .beta-cancel-button {
      background: transparent;
      color: rgba(255, 255, 255, 0.8);
      border: 1px solid rgba(255, 255, 255, 0.3);
      padding: var(--spacing-3) var(--spacing-4);
      border-radius: var(--radius-lg);
      font-size: var(--font-size-sm);
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .beta-cancel-button:hover {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.5);
    }

    .success-message {
      background: var(--color-success-light);
      color: var(--color-success-dark);
      padding: var(--spacing-3);
      border-radius: var(--radius-lg);
      font-size: var(--font-size-sm);
      margin-bottom: var(--spacing-4);
      border: 1px solid var(--color-success);
      text-align: center;
    }

    .beta-success {
      background: rgba(76, 175, 80, 0.2);
      color: white;
      padding: var(--spacing-4);
      border-radius: var(--radius-lg);
      text-align: center;
      border: 1px solid rgba(76, 175, 80, 0.4);
    }

    .beta-success h3 {
      margin: 0 0 var(--spacing-2) 0;
      color: #4CAF50;
    }

    /* Mobile responsive */
    @media (max-width: 480px) {
      .login-container {
        padding: var(--spacing-6);
        margin: var(--spacing-4);
      }

      .beta-actions {
        flex-direction: column;
      }
    }
  `;T([c()],_.prototype,"username",2);T([c()],_.prototype,"password",2);T([c()],_.prototype,"isLoading",2);T([c()],_.prototype,"error",2);T([c()],_.prototype,"showBetaSignup",2);T([c()],_.prototype,"betaEmail",2);T([c()],_.prototype,"betaMessage",2);T([c()],_.prototype,"betaLoading",2);T([c()],_.prototype,"betaError",2);T([c()],_.prototype,"betaSuccess",2);_=T([j("login-form")],_);var St=Object.defineProperty,Ct=Object.getOwnPropertyDescriptor,K=(t,e,i,s)=>{for(var n=s>1?void 0:s?Ct(e,i):e,a=t.length-1,o;a>=0;a--)(o=t[a])&&(n=(s?o(e,i,n):o(n))||n);return s&&n&&St(e,i,n),n};let z=class extends R{constructor(){super(...arguments),this.visible=!1,this.signatureData=null,this.isDrawing=!1,this.hasSignature=!1,this.canvasContext=null,this.lastPoint=null}firstUpdated(){this.setupSignatureCanvas()}setupSignatureCanvas(){this.signatureCanvas&&(this.canvasContext=this.signatureCanvas.getContext("2d"),this.canvasContext&&(this.signatureCanvas.width=400,this.signatureCanvas.height=150,this.canvasContext.strokeStyle="#2c3e50",this.canvasContext.lineWidth=2,this.canvasContext.lineCap="round",this.canvasContext.lineJoin="round",this.addCanvasEventListeners()))}addCanvasEventListeners(){this.signatureCanvas.addEventListener("mousedown",this.startDrawing.bind(this)),this.signatureCanvas.addEventListener("mousemove",this.draw.bind(this)),this.signatureCanvas.addEventListener("mouseup",this.stopDrawing.bind(this)),this.signatureCanvas.addEventListener("mouseout",this.stopDrawing.bind(this)),this.signatureCanvas.addEventListener("touchstart",this.handleTouch.bind(this)),this.signatureCanvas.addEventListener("touchmove",this.handleTouch.bind(this)),this.signatureCanvas.addEventListener("touchend",this.stopDrawing.bind(this))}getEventPoint(t){const e=this.signatureCanvas.getBoundingClientRect(),i=this.signatureCanvas.width/e.width,s=this.signatureCanvas.height/e.height;if(t instanceof MouseEvent)return{x:(t.clientX-e.left)*i,y:(t.clientY-e.top)*s};{const n=t.touches[0]||t.changedTouches[0];return{x:(n.clientX-e.left)*i,y:(n.clientY-e.top)*s}}}startDrawing(t){this.isDrawing=!0,this.lastPoint=this.getEventPoint(t)}draw(t){if(!this.isDrawing||!this.canvasContext||!this.lastPoint)return;const e=this.getEventPoint(t);this.canvasContext.beginPath(),this.canvasContext.moveTo(this.lastPoint.x,this.lastPoint.y),this.canvasContext.lineTo(e.x,e.y),this.canvasContext.stroke(),this.lastPoint=e,this.hasSignature=!0,this.requestUpdate()}handleTouch(t){t.preventDefault(),t.type==="touchstart"?(this.isDrawing=!0,this.lastPoint=this.getEventPoint(t)):t.type==="touchmove"&&this.isDrawing&&this.draw(t)}stopDrawing(){this.isDrawing=!1,this.lastPoint=null}clearSignature(){this.canvasContext&&(this.canvasContext.clearRect(0,0,this.signatureCanvas.width,this.signatureCanvas.height),this.hasSignature=!1,this.signatureData=null,this.requestUpdate())}captureSignature(){if(!this.hasSignature)return null;const t=this.signatureCanvas.toDataURL("image/png"),e={imageData:t,timestamp:Date.now(),isValid:this.validateSignature(t)};return this.storeSignatureTemporarily(e),e}validateSignature(t){const e=document.createElement("canvas"),i=e.getContext("2d");if(!i)return!1;const s=new Image;return s.onload=()=>{e.width=s.width,e.height=s.height,i.drawImage(s,0,0);const a=i.getImageData(0,0,e.width,e.height).data;let o=0;for(let l=0;l<a.length;l+=4){const d=a[l],h=a[l+1],p=a[l+2];a[l+3]>0&&(d<250||h<250||p<250)&&o++}return o>50},s.src=t,!0}storeSignatureTemporarily(t){try{sessionStorage.setItem("wheel-contract-signature",JSON.stringify(t)),console.log("📝 Contract signature stored temporarily")}catch(e){console.warn("Failed to store signature temporarily:",e)}}static getStoredSignature(){try{const t=sessionStorage.getItem("wheel-contract-signature");if(t)return JSON.parse(t)}catch(t){console.warn("Failed to retrieve stored signature:",t)}return null}static clearStoredSignature(){try{sessionStorage.removeItem("wheel-contract-signature"),console.log("🗑️ Contract signature cleared from storage")}catch(t){console.warn("Failed to clear stored signature:",t)}}handleCancel(){this.dispatchEvent(new CustomEvent("contract-cancelled"))}handleAccept(){if(!this.hasSignature)return;const t=this.captureSignature();t&&(this.signatureData=t,this.dispatchEvent(new CustomEvent("contract-accepted",{detail:{signature:t}})))}render(){return r`
      <div class="modal-container" @click=${t=>t.stopPropagation()}>
        <div class="modal-header">
          <span class="modal-icon">⚖️</span>
          <h2 class="modal-title">Activity Contract</h2>
          <p class="modal-subtitle">A moment of conscious commitment</p>
        </div>

        <div class="modal-body">
          <div class="contract-text">
            <p><strong>By spinning this wheel, you acknowledge:</strong></p>
            <ul>
              <li>You are consciously choosing to let the system guide your next activity</li>
              <li>You accept the wheel's decision as a fair and meaningful choice</li>
              <li>You commit to engaging with the selected activity with an open mind</li>
              <li>This is a moment where you temporarily transfer decision authority to the wheel</li>
            </ul>
          </div>

          <div class="authority-moment">
            🎯 <strong>This is your moment of conscious choice</strong> 🎯<br>
            <small>Here lies the power: accepting guidance while maintaining your agency</small>
          </div>

          <div class="signature-section">
            <div class="signature-label">
              Please sign below to accept this contract:
            </div>
            
            <div class="signature-canvas-container ${this.hasSignature?"has-signature":""}">
              <canvas 
                id="signature-canvas"
                width="400" 
                height="150"
              ></canvas>
              <div class="signature-instructions">
                ${this.hasSignature?"✓ Signature captured - you may now accept the contract":"Draw your signature above using mouse or touch"}
              </div>
              
              ${this.hasSignature?r`
                <div class="signature-actions">
                  <button class="clear-signature-btn" @click=${this.clearSignature}>
                    Clear Signature
                  </button>
                </div>
              `:""}
            </div>
          </div>
        </div>

        <div class="modal-actions">
          <button class="action-btn cancel-btn" @click=${this.handleCancel}>
            Cancel
          </button>
          <button 
            class="action-btn accept-btn" 
            ?disabled=${!this.hasSignature}
            @click=${this.handleAccept}
          >
            Accept Contract & Spin
          </button>
        </div>
      </div>
    `}};z.styles=O`
    :host {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 2000;
      background: rgba(0, 0, 0, 0.7);
      backdrop-filter: blur(4px);
      animation: fadeIn 0.3s ease-out;
    }

    :host([visible]) {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
      }
      to {
        opacity: 1;
      }
    }

    .modal-container {
      background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
      border-radius: 20px;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
      max-width: 600px;
      width: 100%;
      max-height: 90vh;
      overflow-y: auto;
      animation: slideIn 0.4s ease-out;
      position: relative;
    }

    @keyframes slideIn {
      from {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
      }
      to {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
    }

    .modal-header {
      text-align: center;
      padding: 30px 30px 20px;
      border-bottom: 2px solid #e9ecef;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 20px 20px 0 0;
    }

    .modal-icon {
      font-size: 3rem;
      margin-bottom: 15px;
      display: block;
    }

    .modal-title {
      font-size: 1.8rem;
      font-weight: 700;
      margin: 0 0 10px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .modal-subtitle {
      font-size: 1rem;
      opacity: 0.9;
      margin: 0;
      font-weight: 300;
    }

    .modal-body {
      padding: 30px;
    }

    .contract-text {
      background: rgba(102, 126, 234, 0.05);
      border-left: 4px solid #667eea;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 25px;
      font-size: 1rem;
      line-height: 1.6;
      color: #2c3e50;
    }

    .authority-moment {
      background: linear-gradient(135deg, #ff6b6b, #ee5a52);
      color: white;
      padding: 15px 20px;
      border-radius: 10px;
      margin: 20px 0;
      text-align: center;
      font-weight: 600;
      box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
    }

    .signature-section {
      margin-top: 25px;
    }

    .signature-label {
      font-size: 1.1rem;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 15px;
      text-align: center;
    }

    .signature-canvas-container {
      border: 3px dashed #667eea;
      border-radius: 12px;
      padding: 15px;
      background: #f8f9fa;
      text-align: center;
      transition: all 0.3s ease;
    }

    .signature-canvas-container.has-signature {
      border-color: #28a745;
      background: rgba(40, 167, 69, 0.05);
      animation: signatureSuccess 0.5s ease-out;
    }

    @keyframes signatureSuccess {
      0% {
        border-color: #667eea;
        background: #f8f9fa;
      }
      50% {
        border-color: #28a745;
        background: rgba(40, 167, 69, 0.1);
        transform: scale(1.02);
      }
      100% {
        border-color: #28a745;
        background: rgba(40, 167, 69, 0.05);
        transform: scale(1);
      }
    }

    #signature-canvas {
      border: 2px solid #dee2e6;
      border-radius: 8px;
      background: white;
      cursor: crosshair;
      touch-action: none;
      max-width: 100%;
    }

    .signature-instructions {
      font-size: 0.9rem;
      color: #6c757d;
      margin-top: 10px;
      font-style: italic;
    }

    .signature-actions {
      display: flex;
      gap: 10px;
      justify-content: center;
      margin-top: 15px;
    }

    .clear-signature-btn {
      background: #6c757d;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 0.9rem;
      transition: all 0.2s ease;
    }

    .clear-signature-btn:hover {
      background: #5a6268;
      transform: translateY(-1px);
    }

    .modal-actions {
      display: flex;
      gap: 15px;
      justify-content: center;
      padding: 0 30px 30px;
    }

    .action-btn {
      padding: 12px 30px;
      border: none;
      border-radius: 8px;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      min-width: 120px;
    }

    .cancel-btn {
      background: #6c757d;
      color: white;
    }

    .cancel-btn:hover {
      background: #5a6268;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
    }

    .accept-btn {
      background: linear-gradient(135deg, #28a745, #20c997);
      color: white;
    }

    .accept-btn:hover:not(:disabled) {
      background: linear-gradient(135deg, #218838, #1ea080);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
    }

    .accept-btn:disabled {
      background: #dee2e6;
      color: #6c757d;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    @media (max-width: 768px) {
      .modal-container {
        margin: 10px;
        max-height: 95vh;
      }

      .modal-header {
        padding: 20px 20px 15px;
      }

      .modal-title {
        font-size: 1.5rem;
      }

      .modal-body {
        padding: 20px;
      }

      #signature-canvas {
        width: 100%;
        height: 120px;
      }

      .modal-actions {
        flex-direction: column;
        padding: 0 20px 20px;
      }
    }
  `;K([w({type:Boolean})],z.prototype,"visible",2);K([c()],z.prototype,"signatureData",2);K([c()],z.prototype,"isDrawing",2);K([c()],z.prototype,"hasSignature",2);K([we("#signature-canvas")],z.prototype,"signatureCanvas",2);z=K([j("contract-disclaimer-modal")],z);/**
 * @license
 * Copyright 2018 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const De="important",$t=" !"+De,Et=re(class extends le{constructor(t){if(super(t),t.type!==oe.ATTRIBUTE||t.name!=="style"||t.strings?.length>2)throw Error("The `styleMap` directive must be used in the `style` attribute and must be the only part in the attribute.")}render(t){return Object.keys(t).reduce((e,i)=>{const s=t[i];return s==null?e:e+`${i=i.includes("-")?i:i.replace(/(?:^(webkit|moz|ms|o)|)(?=[A-Z])/g,"-$&").toLowerCase()}:${s};`},"")}update(t,[e]){const{style:i}=t.element;if(this.ft===void 0)return this.ft=new Set(Object.keys(e)),this.render(e);for(const s of this.ft)e[s]==null&&(this.ft.delete(s),s.includes("-")?i.removeProperty(s):i[s]=null);for(const s in e){const n=e[s];if(n!=null){this.ft.add(s);const a=typeof n=="string"&&n.endsWith($t);s.includes("-")||a?i.setProperty(s,a?n.slice(0,-11):n,a?De:""):i[s]=n}}return ye}});var Mt=Object.defineProperty,At=Object.getOwnPropertyDescriptor,M=(t,e,i,s)=>{for(var n=s>1?void 0:s?At(e,i):e,a=t.length-1,o;a>=0;a--)(o=t[a])&&(n=(s?o(e,i,n):o(n))||n);return s&&n&&Mt(e,i,n),n};let C=class extends R{constructor(){super(...arguments),this.trackerId="",this.workflowType="",this.title="Processing...",this.showMetrics=!0,this.showTimeline=!0,this.compact=!1,this.progress=0,this.currentStage=null,this.stages=[],this.performanceIndicators={speed:"normal",efficiency:1,resourceUsage:"low",bottleneckRisk:0},this.metrics={totalDuration:0,tokensUsed:0,costEstimate:0,averageStageTime:0},this.isCompleted=!1,this.hasError=!1,this.warningMessage="",this.handleProgressUpdate=t=>{const{data:e}=t.detail;if(e.tracker_id!==this.trackerId)return;this.progress=e.progress_percent,this.currentStage={id:e.stage_id,name:e.stage_name,status:e.stage==="error"?"error":e.stage==="completed"?"completed":"active",progress:e.progress_percent,message:e.message,metrics:e.metrics?{tokensUsed:e.metrics.tokens_used,costEstimate:e.metrics.cost_estimate,memoryUsage:e.metrics.memory_usage_mb,cpuUsage:e.metrics.cpu_usage_percent}:void 0};const i=this.stages.findIndex(s=>s.id===e.stage_id);i>=0?this.stages[i]=this.currentStage:this.stages=[...this.stages,this.currentStage],this.isCompleted=e.stage==="completed"&&e.progress_percent>=100,this.hasError=e.stage==="error",e.priority==="critical"?(this.performanceIndicators.speed="critical",this.warningMessage="Critical performance issue detected"):e.priority==="high"&&(this.performanceIndicators.speed="slow",this.warningMessage="Performance degradation detected"),this.requestUpdate()},this.handlePerformanceMetrics=t=>{const{data:e}=t.detail;if(e.tracker_id===this.trackerId){if(this.metrics={totalDuration:e.total_duration_ms,tokensUsed:e.stage_breakdown.reduce((i,s)=>i+(s.tokens_used||0),0),costEstimate:e.stage_breakdown.reduce((i,s)=>i+(s.cost_estimate||0),0),averageStageTime:e.total_duration_ms/e.stage_breakdown.length},this.performanceIndicators.efficiency=e.performance_score,e.bottlenecks.length>0){const i=e.bottlenecks.filter(s=>s.severity==="critical");i.length>0&&(this.performanceIndicators.speed="critical",this.warningMessage=`Critical bottleneck: ${i[0].description}`)}this.requestUpdate()}},this.handleStageTiming=t=>{const{data:e}=t.detail;if(e.tracker_id!==this.trackerId)return;const i=this.stages.findIndex(s=>s.id===e.stage_id);i>=0&&(this.stages[i]={...this.stages[i],duration:e.duration_ms,expectedDuration:e.expected_duration_ms,performanceRatio:e.performance_ratio},this.requestUpdate())},this.handleWorkflowProgress=t=>{const{data:e}=t.detail;this.progress=e.overall_progress,this.performanceIndicators={speed:e.performance_indicators.speed,efficiency:e.performance_indicators.efficiency,resourceUsage:e.performance_indicators.resource_usage,bottleneckRisk:0},this.requestUpdate()}}connectedCallback(){super.connectedCallback(),this.addEventListener("progress-update",this.handleProgressUpdate),this.addEventListener("performance-metrics",this.handlePerformanceMetrics),this.addEventListener("stage-timing",this.handleStageTiming),this.addEventListener("workflow-progress",this.handleWorkflowProgress)}disconnectedCallback(){super.disconnectedCallback(),this.removeEventListener("progress-update",this.handleProgressUpdate),this.removeEventListener("performance-metrics",this.handlePerformanceMetrics),this.removeEventListener("stage-timing",this.handleStageTiming),this.removeEventListener("workflow-progress",this.handleWorkflowProgress)}formatDuration(t){return t<1e3?`${Math.round(t)}ms`:t<6e4?`${(t/1e3).toFixed(1)}s`:`${Math.floor(t/6e4)}m ${Math.floor(t%6e4/1e3)}s`}formatNumber(t){return t>=1e6?`${(t/1e6).toFixed(1)}M`:t>=1e3?`${(t/1e3).toFixed(1)}K`:t.toString()}render(){const t={"progress-bar":!0,fast:this.performanceIndicators.speed==="fast",slow:this.performanceIndicators.speed==="slow",critical:this.performanceIndicators.speed==="critical"},e={width:`${this.progress}%`};return r`
      <div class="progress-container">
        <div class="progress-header">
          <h3 class="progress-title">${this.title}</h3>
          <div class="progress-percentage">${Math.round(this.progress)}%</div>
        </div>

        <div class="progress-bar-container">
          <div class=${ne(t)} style=${Et(e)}></div>
        </div>

        ${this.currentStage?r`
          <div class="current-stage">
            <div class="stage-icon ${this.currentStage.status}">
              ${this.currentStage.status==="completed"?"✓":this.currentStage.status==="error"?"✗":"●"}
            </div>
            <div class="stage-info">
              <p class="stage-name">${this.currentStage.name}</p>
              ${this.currentStage.message?r`
                <p class="stage-message">${this.currentStage.message}</p>
              `:""}
            </div>
          </div>
        `:""}

        ${this.warningMessage?r`
          <div class="warning-indicator">
            <span class="warning-icon">⚠️</span>
            <p class="warning-text">${this.warningMessage}</p>
          </div>
        `:""}

        ${this.showMetrics&&!this.compact?r`
          <div class="performance-metrics">
            <div class="metric-card">
              <p class="metric-value">${this.formatDuration(this.metrics.totalDuration)}</p>
              <p class="metric-label">Duration</p>
            </div>
            <div class="metric-card">
              <p class="metric-value">${this.formatNumber(this.metrics.tokensUsed)}</p>
              <p class="metric-label">Tokens</p>
            </div>
            <div class="metric-card">
              <p class="metric-value">$${this.metrics.costEstimate.toFixed(4)}</p>
              <p class="metric-label">Cost</p>
            </div>
            <div class="metric-card">
              <p class="metric-value">${Math.round(this.performanceIndicators.efficiency*100)}%</p>
              <p class="metric-label">Efficiency</p>
            </div>
          </div>
        `:""}

        ${this.showTimeline&&!this.compact&&this.stages.length>1?r`
          <div class="stages-timeline">
            ${this.stages.map(i=>r`
              <div class="timeline-stage ${i.status}">
                <div class="timeline-icon ${i.status}">
                  ${i.status==="completed"?"✓":i.status==="error"?"✗":i.status==="active"?"●":"○"}
                </div>
                <div class="timeline-content">
                  <p class="timeline-name">${i.name}</p>
                  ${i.duration?r`
                    <p class="timeline-duration">${this.formatDuration(i.duration)}</p>
                  `:""}
                </div>
              </div>
            `)}
          </div>
        `:""}
      </div>
    `}};C.styles=O`
    :host {
      display: block;
      width: 100%;
      font-family: var(--font-family-base, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
    }

    .progress-container {
      background: var(--color-gray-50, #f9fafb);
      border: 1px solid var(--color-gray-200, #e5e7eb);
      border-radius: var(--radius-lg, 12px);
      padding: var(--spacing-4, 16px);
      box-shadow: var(--shadow-sm, 0 1px 2px 0 rgba(0, 0, 0, 0.05));
    }

    .progress-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-3, 12px);
    }

    .progress-title {
      font-size: var(--font-size-lg, 18px);
      font-weight: var(--font-weight-semibold, 600);
      color: var(--color-gray-900, #111827);
      margin: 0;
    }

    .progress-percentage {
      font-size: var(--font-size-xl, 20px);
      font-weight: var(--font-weight-bold, 700);
      color: var(--color-primary, #3b82f6);
    }

    .progress-bar-container {
      position: relative;
      height: 12px;
      background: var(--color-gray-200, #e5e7eb);
      border-radius: 6px;
      overflow: hidden;
      margin-bottom: var(--spacing-4, 16px);
    }

    .progress-bar {
      height: 100%;
      background: linear-gradient(90deg, var(--color-primary, #3b82f6), var(--color-primary-light, #60a5fa));
      border-radius: 6px;
      transition: width 0.3s ease-in-out;
      position: relative;
    }

    .progress-bar.fast {
      background: linear-gradient(90deg, #10b981, #34d399);
    }

    .progress-bar.slow {
      background: linear-gradient(90deg, #f59e0b, #fbbf24);
    }

    .progress-bar.critical {
      background: linear-gradient(90deg, #ef4444, #f87171);
    }

    .progress-bar::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      animation: shimmer 2s infinite;
    }

    @keyframes shimmer {
      0% { transform: translateX(-100%); }
      100% { transform: translateX(100%); }
    }

    .current-stage {
      display: flex;
      align-items: center;
      gap: var(--spacing-2, 8px);
      margin-bottom: var(--spacing-3, 12px);
      padding: var(--spacing-2, 8px);
      background: var(--color-blue-50, #eff6ff);
      border-radius: var(--radius-md, 8px);
      border-left: 4px solid var(--color-primary, #3b82f6);
    }

    .stage-icon {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
      font-weight: bold;
      color: white;
    }

    .stage-icon.active {
      background: var(--color-primary, #3b82f6);
      animation: pulse 2s infinite;
    }

    .stage-icon.completed {
      background: var(--color-success, #10b981);
    }

    .stage-icon.error {
      background: var(--color-error, #ef4444);
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }

    .stage-info {
      flex: 1;
    }

    .stage-name {
      font-weight: var(--font-weight-medium, 500);
      color: var(--color-gray-900, #111827);
      margin: 0 0 2px 0;
      font-size: var(--font-size-sm, 14px);
    }

    .stage-message {
      font-size: var(--font-size-xs, 12px);
      color: var(--color-gray-600, #4b5563);
      margin: 0;
    }

    .performance-metrics {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: var(--spacing-3, 12px);
      margin-top: var(--spacing-4, 16px);
    }

    .metric-card {
      background: white;
      padding: var(--spacing-3, 12px);
      border-radius: var(--radius-md, 8px);
      border: 1px solid var(--color-gray-200, #e5e7eb);
      text-align: center;
    }

    .metric-value {
      font-size: var(--font-size-lg, 18px);
      font-weight: var(--font-weight-bold, 700);
      color: var(--color-gray-900, #111827);
      margin: 0 0 4px 0;
    }

    .metric-label {
      font-size: var(--font-size-xs, 12px);
      color: var(--color-gray-500, #6b7280);
      margin: 0;
    }

    .stages-timeline {
      margin-top: var(--spacing-4, 16px);
      padding-top: var(--spacing-4, 16px);
      border-top: 1px solid var(--color-gray-200, #e5e7eb);
    }

    .timeline-stage {
      display: flex;
      align-items: center;
      gap: var(--spacing-3, 12px);
      padding: var(--spacing-2, 8px) 0;
      position: relative;
    }

    .timeline-stage:not(:last-child)::after {
      content: '';
      position: absolute;
      left: 8px;
      top: 32px;
      width: 2px;
      height: calc(100% - 16px);
      background: var(--color-gray-200, #e5e7eb);
    }

    .timeline-stage.completed::after {
      background: var(--color-success, #10b981);
    }

    .timeline-icon {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
      font-weight: bold;
      color: white;
      flex-shrink: 0;
    }

    .timeline-content {
      flex: 1;
    }

    .timeline-name {
      font-weight: var(--font-weight-medium, 500);
      color: var(--color-gray-900, #111827);
      margin: 0 0 2px 0;
      font-size: var(--font-size-sm, 14px);
    }

    .timeline-duration {
      font-size: var(--font-size-xs, 12px);
      color: var(--color-gray-500, #6b7280);
      margin: 0;
    }

    .warning-indicator {
      background: var(--color-warning-light, #fef3c7);
      border: 1px solid var(--color-warning, #f59e0b);
      border-radius: var(--radius-md, 8px);
      padding: var(--spacing-3, 12px);
      margin-top: var(--spacing-3, 12px);
      display: flex;
      align-items: center;
      gap: var(--spacing-2, 8px);
    }

    .warning-icon {
      color: var(--color-warning, #f59e0b);
      font-size: var(--font-size-lg, 18px);
    }

    .warning-text {
      color: var(--color-warning-dark, #92400e);
      font-size: var(--font-size-sm, 14px);
      margin: 0;
    }

    .hidden {
      display: none;
    }

    @media (max-width: 640px) {
      .performance-metrics {
        grid-template-columns: repeat(2, 1fr);
      }
      
      .progress-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-2, 8px);
      }
    }
  `;M([w({type:String})],C.prototype,"trackerId",2);M([w({type:String})],C.prototype,"workflowType",2);M([w({type:String})],C.prototype,"title",2);M([w({type:Boolean})],C.prototype,"showMetrics",2);M([w({type:Boolean})],C.prototype,"showTimeline",2);M([w({type:Boolean})],C.prototype,"compact",2);M([c()],C.prototype,"progress",2);M([c()],C.prototype,"currentStage",2);M([c()],C.prototype,"stages",2);M([c()],C.prototype,"performanceIndicators",2);M([c()],C.prototype,"metrics",2);M([c()],C.prototype,"isCompleted",2);M([c()],C.prototype,"hasError",2);M([c()],C.prototype,"warningMessage",2);C=M([j("real-time-progress-bar")],C);class H extends EventTarget{constructor(){super(),this.config=null,this.subscribers=new Map,this.state=this.getInitialState()}static getInstance(){return H.instance||(H.instance=new H),H.instance}initialize(e){this.config=e,this.loadPersistedState(),setInterval(()=>{this.persistState()},3e4),window.addEventListener("beforeunload",()=>{this.persistState()}),console.log("✅ State manager initialized")}getInitialState(){return{isConnected:!1,isLoading:!1,currentUser:null,currentWheel:null,chatMessages:[],error:null,lastActivity:Date.now()}}getState(){return{...this.state}}updateState(e){const i={...this.state};this.state={...this.state,...e},this.state.lastActivity=Date.now(),this.notifySubscribers(i),this.dispatchEvent(new CustomEvent("state-updated",{detail:{previousState:i,currentState:this.state,updates:e}})),this.config?.debug&&console.log("🔄 State updated:",e)}setConnectionState(e){this.updateState({isConnected:e})}setLoadingState(e){this.updateState({isLoading:e})}setCurrentUser(e){this.updateState({currentUser:e})}setCurrentWheel(e){this.updateState({currentWheel:e})}addChatMessage(e){const i=[...this.state.chatMessages,e];i.length>100&&i.splice(0,i.length-100),this.updateState({chatMessages:i})}clearChatMessages(){this.updateState({chatMessages:[]})}setError(e){this.updateState({error:e}),e&&console.error("❌ Application error:",e)}clearError(){this.updateState({error:null})}subscribe(e,i){return this.subscribers.has(e)||this.subscribers.set(e,new Set),this.subscribers.get(e).add(i),()=>{const s=this.subscribers.get(e);s&&(s.delete(i),s.size===0&&this.subscribers.delete(e))}}subscribeToProperty(e,i){let s=this.state[e];return this.subscribe(`property:${e}`,n=>{const a=n[e];a!==s&&(i(a,s),s=a)})}notifySubscribers(e){for(const[i,s]of this.subscribers)for(const n of s)try{n(this.state)}catch(a){console.error(`Error in state subscriber ${i}:`,a)}}persistState(){try{const e={currentUser:this.state.currentUser,chatMessages:this.state.chatMessages.slice(-20),lastActivity:this.state.lastActivity};localStorage.setItem("goali-state",JSON.stringify(e))}catch(e){console.warn("Failed to persist state:",e)}}loadPersistedState(){try{const e=localStorage.getItem("goali-state");if(e){const i=JSON.parse(e);this.state={...this.state,currentUser:i.currentUser||null,chatMessages:Array.isArray(i.chatMessages)?i.chatMessages:[],lastActivity:i.lastActivity||Date.now()},console.log("✅ Persisted state loaded")}}catch(e){console.warn("Failed to load persisted state:",e)}}reset(){const e=this.getInitialState();this.state=e,this.notifySubscribers(e);try{localStorage.removeItem("goali-state")}catch(i){console.warn("Failed to clear persisted state:",i)}console.log("🔄 State reset to initial values")}getStateStats(){return{messageCount:this.state.chatMessages.length,subscriberCount:Array.from(this.subscribers.values()).reduce((e,i)=>e+i.size,0),lastActivity:new Date(this.state.lastActivity),memoryUsage:JSON.stringify(this.state).length}}validateState(){const e=[];if(!Array.isArray(this.state.chatMessages))e.push("Chat messages must be an array");else for(const[i,s]of this.state.chatMessages.entries())(!s.id||!s.content||typeof s.isUser!="boolean")&&e.push(`Invalid chat message at index ${i}`);return this.state.currentWheel&&(!this.state.currentWheel.name||!Array.isArray(this.state.currentWheel.items))&&e.push("Invalid wheel data structure"),this.state.currentUser&&(!this.state.currentUser.id||!this.state.currentUser.name)&&e.push("Invalid user profile structure"),{isValid:e.length===0,errors:e}}destroy(){this.persistState(),this.subscribers.clear(),this.config=null}}class G{constructor(){this.debugMode=!1,this.maxDebugMessages=10,this.debugMessageCount=0,this.lastResetTime=Date.now(),this.resetInterval=3e4}static getInstance(){return G.instance||(G.instance=new G),G.instance}setDebugMode(e){this.debugMode=e}shouldShowDebugMessage(e){if(!this.debugMode)return!1;const i=Date.now();return i-this.lastResetTime>this.resetInterval&&(this.resetDebugMessageCount(),this.lastResetTime=i),this.debugMessageCount>=this.maxDebugMessages?(this.debugMessageCount===this.maxDebugMessages&&(console.warn("🚫 Debug message limit reached. Further debug messages will be suppressed for 30 seconds."),this.debugMessageCount++),!1):(this.debugMessageCount++,!0)}resetDebugMessageCount(){this.debugMessageCount=0}shouldAddToChat(e){return e!=="debug_info"}getDebugMessageCount(){return this.debugMessageCount}}class he{static STORAGE_KEY="conversationState";static get(){try{const e=sessionStorage.getItem(this.STORAGE_KEY);return e?JSON.parse(e):{}}catch(e){return console.warn("Failed to parse conversation state from session storage:",e),{}}}static set(e){try{sessionStorage.setItem(this.STORAGE_KEY,JSON.stringify(e))}catch(i){console.error("Failed to store conversation state:",i)}}static update(e){const i=this.get();this.set({...i,...e})}static clear(){try{sessionStorage.removeItem(this.STORAGE_KEY)}catch(e){console.error("Failed to clear conversation state:",e)}}static getField(e,i=null){const s=this.get();return s[e]!==void 0?s[e]:i}static setField(e,i){this.update({[e]:i})}static isInPhase(e){return this.getField("phase")===e}static isAwaitingResponse(e){return this.getField("awaiting_response_type")===e}static getContext(){return this.getField("context",{})}static updateContext(e){const i=this.getContext();this.setField("context",{...i,...e})}static validateState(e){const i=["initial","awaiting_profile_info","awaiting_situation_info","awaiting_activity_feedback"],s=["profile_info","situation_info","activity_selection","activity_feedback"];return(!e.phase||!i.includes(e.phase))&&(e.phase="initial"),e.awaiting_response_type&&!s.includes(e.awaiting_response_type)&&delete e.awaiting_response_type,(!e.context||typeof e.context!="object")&&(e.context={}),e}static initialize(e={}){const i=this.validateState(e);this.set(i)}static debug(){console.log("🔄 Conversation State:",this.get())}}class V extends EventTarget{constructor(){super(),this.wsManager=null,this.stateManager=null,this.messageId=0,this.debugFilter=G.getInstance()}static getInstance(){return V.instance||(V.instance=new V),V.instance}initialize(e,i){this.wsManager=e,this.stateManager=i,this.wsManager.addEventListener("message",this.handleMessage.bind(this)),this.wsManager.addEventListener("open",this.handleConnectionOpen.bind(this)),this.wsManager.addEventListener("close",this.handleConnectionClose.bind(this)),this.wsManager.addEventListener("error",this.handleConnectionError.bind(this)),console.log("✅ Message handler initialized")}handleMessage(e){const{data:i}=e.detail;try{switch(i.type){case"system_message":this.handleSystemMessage(i);break;case"chat_message":this.handleChatMessage(i);break;case"processing_status":this.handleProcessingStatus(i);break;case"wheel_data":this.handleWheelData(i);break;case"activity_details":this.handleActivityDetails(i);break;case"error":this.handleError(i);break;case"debug_info":this.handleDebugInfo(i);break;case"workflow_status":this.handleWorkflowStatus(i);break;case"conversation_state_update":this.handleConversationStateUpdate(i);break;default:console.warn("Unknown message type:",i.type)}}catch(s){console.error("Error handling message:",s),this.stateManager?.setError("Failed to process server message")}}handleSystemMessage(e){console.log("📢 System message:",e.content);const i={id:`sys_${++this.messageId}`,content:e.content,isUser:!1,timestamp:Date.now(),type:"system"};this.stateManager?.addChatMessage(i),this.dispatchEvent(new CustomEvent("system-message",{detail:{message:e.content}}))}handleChatMessage(e){const i={id:`chat_${++this.messageId}`,content:e.content,isUser:e.is_user,timestamp:Date.now(),type:"text"};this.stateManager?.addChatMessage(i),this.dispatchEvent(new CustomEvent("chat-message",{detail:{message:i}}))}handleProcessingStatus(e){const i=e.status==="processing";this.stateManager?.setLoadingState(i),e.status==="completed"&&this.stateManager?.clearError(),this.dispatchEvent(new CustomEvent("processing-status",{detail:{status:e.status}}))}handleWheelData(e){const i={name:e.wheel.name,items:e.wheel.items.map(n=>({id:n.id,name:n.name,description:n.description,percentage:n.percentage,color:n.color,domain:n.domain,base_challenge_rating:n.base_challenge_rating,activity_tailored_id:n.activity_tailored_id}))},s=this.validateWheelData(i);if(!s.isValid){console.error("Invalid wheel data:",s.errors),this.stateManager?.setError("Received invalid wheel data");return}this.stateManager?.setCurrentWheel(i),this.stateManager?.setLoadingState(!1),this.dispatchEvent(new CustomEvent("wheel-data",{detail:{wheel:i,mentorContext:e.mentor_context,workflowInsights:e.workflow_insights}}))}handleActivityDetails(e){const i={id:e.details.id,name:e.details.name,detailed_description:e.details.detailed_description,preparation_steps:e.details.preparation_steps,tips_for_success:e.details.tips_for_success,reflection_questions:e.details.reflection_questions};this.dispatchEvent(new CustomEvent("activity-details",{detail:{activity:i}}))}handleError(e){const i=typeof e.content=="string"?e.content:e.content.content;console.error("❌ Server error:",i),this.stateManager?.setError(i),this.stateManager?.setLoadingState(!1);const s={id:`error_${++this.messageId}`,content:`⚠️ ${i}`,isUser:!1,timestamp:Date.now(),type:"error"};this.stateManager?.addChatMessage(s),this.dispatchEvent(new CustomEvent("error",{detail:{error:i,code:typeof e.content=="object"?e.content.code:void 0,details:typeof e.content=="object"?e.content.details:void 0}}))}handleDebugInfo(e){this.debugFilter.shouldShowDebugMessage(e)&&console.log("🐛 Debug info:",e),this.dispatchEvent(new CustomEvent("debug-info",{detail:{info:e.content||e,timestamp:Date.now()}}))}handleWorkflowStatus(e){console.log("🔄 Workflow status:",e),e.status==="running"||e.status==="processing"?this.stateManager?.setLoadingState(!0):(e.status==="completed"||e.status==="failed")&&this.stateManager?.setLoadingState(!1),this.dispatchEvent(new CustomEvent("workflow-status",{detail:{status:e.status,workflow:e.workflow,details:e.details,timestamp:Date.now()}}))}handleConversationStateUpdate(e){console.log("🔄 Conversation state update:",e),e.updates&&typeof e.updates=="object"&&(he.update(e.updates),this.dispatchEvent(new CustomEvent("conversation-state-update",{detail:{updates:e.updates,timestamp:Date.now()}})))}handleConnectionOpen(){this.stateManager?.setConnectionState(!0),this.stateManager?.clearError(),console.log("✅ Connected to server")}handleConnectionClose(){this.stateManager?.setConnectionState(!1),this.stateManager?.setLoadingState(!1),console.log("🔌 Disconnected from server")}handleConnectionError(){this.stateManager?.setConnectionState(!1),this.stateManager?.setError("Connection error occurred"),console.error("❌ Connection error")}async sendChatMessage(e,i,s,n,a){if(!this.wsManager)throw new Error("WebSocket manager not initialized");const o={id:`user_${++this.messageId}`,content:e,isUser:!0,timestamp:Date.now(),type:"text"};this.stateManager?.addChatMessage(o);const l={};s&&(l.requested_workflow=s),n&&(l.llm_config_id=n),a&&Object.assign(l,a);const d=he.get();l.conversation_phase=d.phase||"initial",l.awaiting_response_type=d.awaiting_response_type,l.last_workflow=d.last_workflow,l.session_context=d.context||{},await this.wsManager.send({type:"chat_message",content:{message:e,user_profile_id:i,timestamp:new Date().toISOString(),metadata:l}}),this.stateManager?.setLoadingState(!0)}async sendSpinResult(e,i,s,n){if(!this.wsManager)throw new Error("WebSocket manager not initialized");const a=he.get();await this.wsManager.send({type:"spin_result",content:{activity_tailored_id:e,name:i,description:s,user_profile_id:n,metadata:{conversation_phase:a.phase||"initial",awaiting_response_type:a.awaiting_response_type,last_workflow:a.last_workflow,session_context:a.context||{}}}}),this.stateManager?.setLoadingState(!0)}validateWheelData(e){const i=[];e.name||i.push("Wheel name is required"),(!Array.isArray(e.items)||e.items.length===0)&&i.push("Wheel must have at least one item");const s=e.items.reduce((n,a)=>n+a.percentage,0);if(s<=0)i.push("Wheel percentages must be positive values");else if(Math.abs(s-100)>.01){console.log(`🔧 Auto-fixing wheel percentages: total was ${s.toFixed(2)}, normalizing to 100`);const n=100/s;e.items.forEach(o=>{o.percentage=Math.round(o.percentage*n*100)/100});const a=e.items.reduce((o,l)=>o+l.percentage,0);console.log(`✅ Percentages normalized: new total = ${a.toFixed(2)}`)}for(const[n,a]of e.items.entries())(!a.id||!a.name||!a.color)&&i.push(`Invalid item at index ${n}: missing required fields`),(a.percentage<=0||a.percentage>100)&&i.push(`Invalid percentage for item ${a.name}: ${a.percentage}`);return{isValid:i.length===0,errors:i}}destroy(){this.wsManager&&(this.wsManager.removeEventListener("message",this.handleMessage.bind(this)),this.wsManager.removeEventListener("open",this.handleConnectionOpen.bind(this)),this.wsManager.removeEventListener("close",this.handleConnectionClose.bind(this)),this.wsManager.removeEventListener("error",this.handleConnectionError.bind(this))),this.wsManager=null,this.stateManager=null}}class Y extends EventTarget{constructor(){super(),this.ws=null,this.config=null,this.messageQueue=[],this.heartbeatInterval=null,this.reconnectTimeout=null,this.isReconnecting=!1,this.messageId=0,this.state={readyState:WebSocket.CLOSED,url:"",protocol:"",isConnected:!1,lastPingTime:0,reconnectAttempts:0}}static getInstance(){return Y.instance||(Y.instance=new Y),Y.instance}async initialize(e){return this.config=e,this.state.url=e.url,new Promise((i,s)=>{try{this.connect();const n=()=>{this.removeEventListener("open",n),this.removeEventListener("error",a),i()},a=o=>{this.removeEventListener("open",n),this.removeEventListener("error",a),s(new Error("Failed to establish WebSocket connection"))};this.addEventListener("open",n),this.addEventListener("error",a),setTimeout(()=>{this.removeEventListener("open",n),this.removeEventListener("error",a),s(new Error("WebSocket connection timeout"))},4e4)}catch(n){s(n)}})}connect(){if(!this.config)throw new Error("WebSocket manager not initialized");if(!(this.ws&&this.ws.readyState===WebSocket.OPEN))try{this.ws=new WebSocket(this.config.url),this.setupEventListeners(),this.updateState()}catch(e){console.error("Failed to create WebSocket connection:",e),this.scheduleReconnect()}}setupEventListeners(){this.ws&&(this.ws.onopen=e=>{console.log("✅ WebSocket connected"),this.state.isConnected=!0,this.state.reconnectAttempts=0,this.isReconnecting=!1,this.updateState(),this.startHeartbeat(),this.processMessageQueue(),window.__GOALI_WS__=this.ws,this.dispatchEvent(new CustomEvent("open",{detail:e})),this.dispatchEvent(new CustomEvent("connection-change",{detail:!0})),window.dispatchEvent(new CustomEvent("websocket-connected",{detail:!0}))},this.ws.onclose=e=>{const i=e.code===1e3,s=e.reason||this.getCloseReasonText(e.code);console.log(`🔌 WebSocket disconnected: ${e.code} (${s})`),this.state.isConnected=!1,this.state.lastError=i?null:`Connection closed: ${s}`,this.updateState(),this.stopHeartbeat(),this.dispatchEvent(new CustomEvent("close",{detail:{event:e,code:e.code,reason:s,wasClean:i}})),this.dispatchEvent(new CustomEvent("connection-change",{detail:!1})),!i&&this.config&&(console.log("🔄 Scheduling reconnection due to unexpected close"),this.scheduleReconnect())},this.ws.onerror=e=>{const i="WebSocket connection error occurred";console.error("❌ WebSocket error:",e),this.state.lastError=i,this.updateState(),this.dispatchEvent(new CustomEvent("error",{detail:{event:e,message:i,timestamp:Date.now()}}))},this.ws.onmessage=e=>{this.handleMessage(e)})}handleMessage(e){try{const i=JSON.parse(e.data),s=this.validateServerMessage(i);if(!s.isValid){console.warn("Invalid message received:",s.errors);return}if(i.type==="system_message"&&i.content==="pong"){this.dispatchEvent(new CustomEvent("pong"));return}this.dispatchEvent(new CustomEvent("message",{detail:{data:i,originalEvent:e}})),this.dispatchEvent(new CustomEvent(`message-${i.type}`,{detail:i}))}catch(i){console.error("Failed to parse WebSocket message:",i)}}send(e){return new Promise((i,s)=>{if(!this.isConnected()){this.queueMessage(e),i();return}try{const n=this.validateClientMessage(e);if(!n.isValid){s(new Error(`Invalid message: ${n.errors.join(", ")}`));return}const a=JSON.stringify(e);this.ws.send(a),i()}catch(n){console.error("Failed to send message:",n),this.queueMessage(e),s(n)}})}queueMessage(e){const i={id:`msg_${++this.messageId}`,message:e,timestamp:Date.now(),retryCount:0,maxRetries:3};this.messageQueue.push(i),this.messageQueue.length>100&&this.messageQueue.shift()}async processMessageQueue(){if(!this.isConnected()||this.messageQueue.length===0)return;const e=[...this.messageQueue];this.messageQueue=[];for(const i of e)try{await this.send(i.message)}catch(s){i.retryCount++,i.retryCount<i.maxRetries?this.messageQueue.push(i):console.error("Failed to send queued message after retries:",s)}}startHeartbeat(){!this.config||this.heartbeatInterval||(this.heartbeatInterval=window.setInterval(()=>{this.isConnected()&&this.ping()},this.config.heartbeatInterval))}stopHeartbeat(){this.heartbeatInterval&&(clearInterval(this.heartbeatInterval),this.heartbeatInterval=null)}ping(){this.state.lastPingTime=Date.now(),console.debug("Ping skipped - using connection state check instead")}scheduleReconnect(){if(!this.config||this.isReconnecting)return;if(this.state.reconnectAttempts>=this.config.reconnectAttempts){console.error("Max reconnection attempts reached"),this.dispatchEvent(new CustomEvent("max-reconnects-reached"));return}this.isReconnecting=!0,this.state.reconnectAttempts++;const e=this.config.reconnectDelay*Math.pow(2,this.state.reconnectAttempts-1);console.log(`Reconnecting in ${e}ms (attempt ${this.state.reconnectAttempts})`),this.reconnectTimeout=window.setTimeout(()=>{this.connect()},e)}getCloseReasonText(e){return{1e3:"Normal closure",1001:"Going away",1002:"Protocol error",1003:"Unsupported data",1004:"Reserved",1005:"No status received",1006:"Abnormal closure",1007:"Invalid frame payload data",1008:"Policy violation",1009:"Message too big",1010:"Mandatory extension",1011:"Internal server error",1012:"Service restart",1013:"Try again later",1014:"Bad gateway",1015:"TLS handshake"}[e]||`Unknown close code: ${e}`}updateState(){this.ws&&(this.state.readyState=this.ws.readyState,this.state.protocol=this.ws.protocol)}isConnected(){return this.ws?.readyState===WebSocket.OPEN&&this.state.isConnected}getState(){return{...this.state}}validateClientMessage(e){const i=[];return e.type||i.push("Message type is required"),e.content||i.push("Message content is required"),e.type==="chat_message"&&(e.content.message||i.push("Chat message content is required"),e.content.user_profile_id||i.push("User profile ID is required")),{isValid:i.length===0,errors:i}}validateServerMessage(e){const i=[];return e.type||i.push("Message type is required"),{isValid:i.length===0,errors:i}}close(){this.stopHeartbeat(),this.reconnectTimeout&&(clearTimeout(this.reconnectTimeout),this.reconnectTimeout=null),this.ws&&(this.ws.close(1e3,"Client closing"),this.ws=null),this.state.isConnected=!1,this.updateState()}onMessage(e,i){this.addEventListener(`message-${e}`,s=>{i(s.detail)})}onConnectionChange(e){this.addEventListener("connection-change",i=>{e(i.detail)})}sendMessage(e,i){const s={type:e,content:i};this.send(s)}disconnect(){this.close()}destroy(){this.close(),this.messageQueue=[],this.config=null}}var _t=Object.defineProperty,Pt=Object.getOwnPropertyDescriptor,m=(t,e,i,s)=>{for(var n=s>1?void 0:s?Pt(e,i):e,a=t.length-1,o;a>=0;a--)(o=t[a])&&(n=(s?o(e,i,n):o(n))||n);return s&&n&&_t(e,i,n),n};let u=class extends R{constructor(){super(...arguments),this.messages=[],this.wheelData=null,this.wsConnected=!1,this.isLoading=!1,this.error=null,this.isAuthenticated=!1,this.showDebugPanel=!1,this.currentMode="debug",this.debugUserId="",this.debugLLMConfigId="",this.timeAvailable=50,this.showProgressBar=!1,this.currentProgressTrackerId="",this.progressTitle="Processing...",this.energyLevel=50,this.currentUser=null,this.connectionState="disconnected",this.expandedActivities=new Set,this.showActivityModal=!1,this.selectedActivityId=null,this.searchQuery="",this.activityCatalog=[],this.activityCatalogCache={data:null,timestamp:0,userId:null},this.ACTIVITY_CACHE_TTL=5*60*1e3,this.showCreateActivityModal=!1,this.newActivityForm={name:"",description:"",domain:"general",base_challenge_rating:50},this.showProfileModal=!1,this.currentUserProfile=null,this.showWinningModal=!1,this.winningActivity=null,this.expandedProfileSections=new Set(["basic"]),this.editingProfileFields=new Set,this.showFeedbackModal=!1,this.feedbackModalConfig={title:"",message:"",feedback_type:"",content_type:"",object_id:""},this.showAddActivityModal=!1,this.showContractModal=!1,this.showEnjoyOverlay=!1,this.enjoyOverlayActivity=null,this.enjoyOverlayStartTime=null,this.showPostActivityFeedbackModal=!1,this.postActivityFeedbackConfig={title:"",message:"",feedback_type:"",content_type:"",object_id:"",activity:null},this.stateManager=H.getInstance(),this.messageHandler=V.getInstance(),this.websocketManager=Y.getInstance(),this.configService=B.getInstance(),this.authService=F.getInstance(),this.debugFilter=G.getInstance(),this.handleAuthChange=t=>{if(this.isAuthenticated=t.detail.isAuthenticated,this.isAuthenticated){const e=this.authService.getCurrentUser();e&&(this.currentUser={id:e.id,name:e.name||e.username||"User",isStaff:e.is_staff||!1}),this.initializeApp()}else this.currentUser=null;this.requestUpdate()},this.handleConfigChange=t=>{console.log("Configuration changed:",t.detail.config)},this.handleKeydown=t=>{t.ctrlKey&&t.shiftKey&&t.key==="D"&&this.configService.isDebugMode()&&(t.preventDefault(),this.showDebugPanel=!this.showDebugPanel)},this.handleLoadMockedWheel=t=>{console.log("🎡 Loading mocked wheel items from debug panel...");const e={segments:[{id:"mock-1",text:"🏃‍♂️ Morning Run",percentage:15,color:"#FF6B6B",description:"Start your day with an energizing 20-minute jog around the neighborhood"},{id:"mock-2",text:"📚 Study Session",percentage:20,color:"#4ECDC4",description:"Focus on your most challenging subject for 45 minutes with breaks"},{id:"mock-3",text:"🎨 Creative Drawing",percentage:12,color:"#45B7D1",description:"Express yourself through art - sketch, paint, or digital drawing"},{id:"mock-4",text:"🧘‍♀️ Meditation",percentage:10,color:"#96CEB4",description:"Find inner peace with a 15-minute mindfulness session"},{id:"mock-5",text:"🍳 Cooking",percentage:18,color:"#FFEAA7",description:"Try a new healthy recipe and enjoy the process of cooking"},{id:"mock-6",text:"🎵 Music Practice",percentage:13,color:"#DDA0DD",description:"Practice your instrument or learn a new song for 30 minutes"},{id:"mock-7",text:"📞 Call Friend",percentage:8,color:"#98FB98",description:"Reconnect with a friend you haven't spoken to in a while"},{id:"mock-8",text:"🌱 Garden Care",percentage:4,color:"#F0E68C",description:"Tend to your plants or start a small herb garden"}]};this.wheelData=e;const i={id:`mock-loaded-${Date.now()}`,type:"ai",content:"🎡 Perfect! I've loaded a diverse set of activities for you to try. Give the wheel a spin to see what adventure awaits!",timestamp:new Date};this.messages=[...this.messages,i],console.log("✅ Mock wheel data loaded successfully")},this.closePostActivityFeedbackModal=()=>{this.showPostActivityFeedbackModal=!1;try{localStorage.removeItem("goali_last_wheel_spin")}catch(t){console.warn("Failed to clean up wheel spin data:",t)}},this.submitPostActivityFeedback=async()=>{try{const t=this.postActivityFeedbackConfig.activity,e=document.getElementById("post-activity-feedback-comment"),i=document.querySelector('input[name="experience-rating"]:checked'),s=document.querySelector('input[name="difficulty-rating"]:checked'),n=document.querySelector('input[name="enjoyment-rating"]:checked'),a=document.querySelector('input[name="recommendation-rating"]:checked'),o={activity_id:t.id,activity_name:t.name,activity_domain:t.domain,activity_type:t.type,activity_challenge_rating:t.base_challenge_rating,activity_duration_range:t.duration_range,experience_rating:i?parseInt(i.value):null,difficulty_rating:s?parseInt(s.value):null,enjoyment_rating:n?parseInt(n.value):null,recommendation_rating:a?parseInt(a.value):null,feedback_timestamp:new Date().toISOString(),session_duration_minutes:Math.round((Date.now()-JSON.parse(localStorage.getItem("goali_last_wheel_spin")||"{}").timestamp)/(1e3*60))};(await fetch(`${this.getBackendBaseUrl()}/api/feedback/`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.authService.getToken()}`},body:JSON.stringify({feedback_type:this.postActivityFeedbackConfig.feedback_type,content_type:this.postActivityFeedbackConfig.content_type,object_id:this.postActivityFeedbackConfig.object_id,user_comment:e?.value||"",criticality:1,context_data:o,wheel_item_id:t.wheel_item_id||t.id})})).ok?(console.log("✅ Post-activity feedback submitted successfully"),this.closePostActivityFeedbackModal(),this.resetAppAfterFeedback()):(console.error("❌ Failed to submit post-activity feedback"),this.error="Failed to submit feedback. Please try again.")}catch(t){console.error("❌ Error submitting post-activity feedback:",t),this.error="Failed to submit feedback. Please try again."}},this.handleTimeAvailableChange=t=>{const e=t.target;this.timeAvailable=parseInt(e.value)},this.handleEnergyLevelChange=t=>{const e=t.target;this.energyLevel=parseInt(e.value)},this.handleGenerateWheel=async()=>{if(!(!this.wsConnected||this.isLoading))try{this.isLoading=!0,this.error=null;const t=this.debugUserId||"2",e=Math.round(10+this.timeAvailable/100*230),i=`Generate a personalized activity wheel (Energy: ${this.energyLevel}%, Time: ${e} minutes)`;console.log("🎯 Sending wheel generation request with energy level:",this.energyLevel,"and time available:",e,"minutes"),await this.messageHandler.sendChatMessage(i,t,"wheel_generation",this.configService.isDebugMode()?this.debugLLMConfigId:void 0,{forced_wheel_generation:!0,energy_level:this.energyLevel,time_available_minutes:e})}catch(t){console.error("❌ Failed to generate wheel:",t),this.error="Failed to generate wheel. Please try again.",this.isLoading=!1}},this.handleSpinWheel=()=>{if(!this.wheelData||this.isLoading){console.log("[APP] Cannot spin: wheelData missing or loading in progress");return}console.log("[APP] Spin wheel button clicked - showing contract modal..."),this.showContractModal=!0},this.handleContractAccepted=t=>{console.log("[APP] Contract accepted, proceeding with wheel spin...",t.detail),this.showContractModal=!1,t.detail.signature&&console.log("📝 Contract signature captured for winning modal"),this.performWheelSpin()},this.handleContractCancelled=()=>{console.log("[APP] Contract cancelled by user"),this.showContractModal=!1},this.toggleActivityExpanded=t=>{const e=new Set(this.expandedActivities);e.has(t)?e.delete(t):e.add(t),this.expandedActivities=e},this.openActivityModal=t=>{this.selectedActivityId=t,this.showActivityModal=!0,this.invalidateActivityCache(),this.loadActivityCatalog()},this.closeActivityModal=()=>{this.showActivityModal=!1,this.selectedActivityId=null,this.searchQuery=""},this.handleSearchChange=t=>{const e=t.target;this.searchQuery=e.value,clearTimeout(this.searchTimeout),this.searchTimeout=setTimeout(()=>{this.performSearch()},300)},this.selectCatalogActivity=async t=>{if(!this.selectedActivityId||!this.wheelData){console.error("No activity selected or no wheel data available");return}console.log("Changing activity from",this.selectedActivityId,"to:",t);let e=t;if(t.type==="generic")try{this.isLoading=!0,this.requestUpdate(),console.log("🔄 Auto-tailoring generic activity:",t.name);const o=this.configService.getConfig().websocket.url.replace("ws://","http://").replace("wss://","https://").replace("/ws/game/",""),l=await fetch(`${o}/api/activities/tailor/`,{method:"POST",headers:{"Content-Type":"application/json",...this.authService.getToken()?{Authorization:`Bearer ${this.authService.getToken()}`}:{}},body:JSON.stringify({generic_activity_id:t.id})});if(l.ok){const d=await l.json();d.success&&d.activity?(e=d.activity,console.log("✅ Activity auto-tailored successfully:",e.name),this.activityCatalog=[...this.activityCatalog,e],this.invalidateActivityCache()):console.warn("⚠️ Failed to tailor activity, using generic version")}else console.warn("⚠️ Tailor API failed, using generic version")}catch(a){console.error("❌ Error auto-tailoring activity:",a),console.log("Using generic version as fallback")}finally{this.isLoading=!1,this.requestUpdate()}const i=this.wheelData.segments.findIndex(a=>a.id===this.selectedActivityId||a.activityId===this.selectedActivityId||a.activity_tailored_id===this.selectedActivityId);if(i===-1){console.error("Activity not found in wheel data:",this.selectedActivityId);return}const s=[...this.wheelData.segments],n=s[i];s[i]={...n,id:e.id,name:e.name,text:e.name,description:e.description,domain:e.domain,base_challenge_rating:e.base_challenge_rating,activityId:e.id,activity_tailored_id:e.id},this.wheelData={...this.wheelData,segments:s},console.log("✅ Activity replaced successfully"),this.closeActivityModal()},this.openCreateActivityModal=()=>{this.showCreateActivityModal=!0,this.newActivityForm={name:"",description:"",domain:"general",base_challenge_rating:50}},this.closeCreateActivityModal=()=>{this.showCreateActivityModal=!1},this.closeProfileModal=()=>{this.showProfileModal=!1,this.editingProfileFields.clear()},this.closeWinningModal=()=>{const t=this.winningActivity;this.showWinningModal=!1,this.winningActivity=null,z.clearStoredSignature(),t&&this.showEnjoyOverlayWithActivity(t)},this.toggleProfileSection=t=>{const e=new Set(this.expandedProfileSections);e.has(t)?e.delete(t):e.add(t),this.expandedProfileSections=e},this.startEditingField=t=>{const e=new Set(this.editingProfileFields);e.add(t),this.editingProfileFields=e},this.stopEditingField=t=>{const e=new Set(this.editingProfileFields);e.delete(t),this.editingProfileFields=e},this.handleRemoveActivity=(t,e)=>{t.stopPropagation(),this.feedbackModalConfig={title:"Don't like this one ?",message:"Please tell us more about the reason why you don't want this in your wheel",feedback_type:"wheel_item_refusal",content_type:"WheelItem",object_id:e.id},this.showFeedbackModal=!0},this.openAddActivityModal=()=>{this.showAddActivityModal=!0,this.searchQuery="",this.loadActivityCatalog()},this.closeAddActivityModal=()=>{this.showAddActivityModal=!1,this.searchQuery=""},this.closeFeedbackModal=()=>{this.showFeedbackModal=!1},this.submitFeedback=async()=>{try{(await fetch(`${this.getBackendBaseUrl()}/api/feedback/`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.authService.getToken()}`},body:JSON.stringify({feedback_type:this.feedbackModalConfig.feedback_type,content_type:this.feedbackModalConfig.content_type,object_id:this.feedbackModalConfig.object_id,user_comment:document.getElementById("feedback-comment")?.value||"",criticality:1,context_data:{}})})).ok?(await this.removeWheelItem(this.feedbackModalConfig.object_id),this.closeFeedbackModal()):console.error("Failed to submit feedback")}catch(t){console.error("Error submitting feedback:",t)}},this.removeWheelItem=async t=>{try{const e=await fetch(`${this.getBackendBaseUrl()}/api/wheel-items/${t}/`,{method:"DELETE",headers:{Authorization:`Bearer ${this.authService.getToken()}`}});if(e.ok){const i=await e.json();i.success&&i.wheel_data&&(this.wheelData={segments:i.wheel_data.segments.map(s=>({id:s.id,text:s.name,name:s.name,description:s.description,percentage:s.percentage,color:s.color,activity_tailored_id:s.activity_tailored_id}))})}else console.error("Failed to remove wheel item")}catch(e){console.error("Error removing wheel item:",e)}},this.addActivityToWheel=async t=>{try{const e=await fetch(`${this.getBackendBaseUrl()}/api/wheel-items/`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.authService.getToken()}`},body:JSON.stringify({activity_id:t.id,activity_type:t.type})});if(e.ok){const i=await e.json();i.success&&i.wheel_data&&(this.wheelData={segments:i.wheel_data.segments.map(s=>({id:s.id,text:s.name,name:s.name,description:s.description,percentage:s.percentage,color:s.color,activity_tailored_id:s.activity_tailored_id}))},this.closeAddActivityModal())}else{const i=await e.json();console.error("Failed to add activity to wheel:",i.error),alert(i.error||"Failed to add activity to wheel")}}catch(e){console.error("Error adding activity to wheel:",e)}},this.saveProfileField=async(t,e)=>{if(console.log(`Saving profile field ${t}:`,e),this.currentUserProfile){const i=t.split(".");let s=this.currentUserProfile;for(let n=0;n<i.length-1;n++)s[i[n]]||(s[i[n]]={}),s=s[i[n]];s[i[i.length-1]]=e,this.requestUpdate()}this.stopEditingField(t)},this.handleNewActivityFormChange=(t,e)=>{this.newActivityForm={...this.newActivityForm,[t]:e}},this.createNewActivity=async()=>{if(!this.newActivityForm.name.trim()){alert("Please enter an activity name");return}if(!this.newActivityForm.description.trim()){alert("Please enter an activity description");return}try{console.log("Creating new activity:",this.newActivityForm),this.isLoading=!0,this.requestUpdate();const e=this.configService.getConfig().websocket.url.replace("ws://","http://").replace("wss://","https://").replace("/ws/game/",""),i=await fetch(`${e}/api/activities/create/`,{method:"POST",headers:{"Content-Type":"application/json",...this.authService.getToken()?{Authorization:`Bearer ${this.authService.getToken()}`}:{}},body:JSON.stringify({name:this.newActivityForm.name,description:this.newActivityForm.description,domain:this.newActivityForm.domain,base_challenge_rating:this.newActivityForm.base_challenge_rating,duration_range:"15-30 minutes",instructions:this.newActivityForm.description})});if(i.ok){const s=await i.json();if(s.success&&s.activity){const n=s.activity;this.activityCatalog=[...this.activityCatalog,n],this.invalidateActivityCache(),this.closeCreateActivityModal(),console.log("✅ New activity created successfully:",n.name),this.selectedActivityId&&await this.selectCatalogActivity(n)}else throw new Error(s.error||"Failed to create activity")}else{const s=await i.json();throw new Error(s.error||"Failed to create activity")}}catch(t){console.error("Failed to create new activity:",t);const e=t instanceof Error?t.message:"Unknown error";alert(`Failed to create new activity: ${e}`)}finally{this.isLoading=!1,this.requestUpdate()}},this.openFullProfileEditor=()=>{const t=`${window.location.origin}/admin/user-profiles/`;window.open(t,"_blank"),this.closeProfileModal()}}connectedCallback(){super.connectedCallback(),this.loadDebugSelections(),this.loadCurrentUser(),this.checkForActiveEnjoyOverlay(),this.initializeApp(),this.setupEventListeners()}disconnectedCallback(){super.disconnectedCallback(),this.websocketManager.disconnect(),this.removeEventListeners()}setupEventListeners(){window.addEventListener("auth-changed",this.handleAuthChange),window.addEventListener("config-changed",this.handleConfigChange),window.addEventListener("keydown",this.handleKeydown),window.addEventListener("load-mocked-wheel",this.handleLoadMockedWheel)}removeEventListeners(){window.removeEventListener("auth-changed",this.handleAuthChange),window.removeEventListener("config-changed",this.handleConfigChange),window.removeEventListener("keydown",this.handleKeydown),window.removeEventListener("load-mocked-wheel",this.handleLoadMockedWheel)}async initializeApp(){try{if(this.isLoading=!0,this.connectionState="connecting",this.currentMode=this.configService.getMode(),this.isAuthenticated||(this.isAuthenticated=this.authService.isAuthenticated()),!this.isAuthenticated){this.isLoading=!1;return}const t=this.configService.getConfig();let e=!1,i=null;try{console.log("🔌 Attempting WebSocket connection to:",t.websocket.url);const s=this.websocketManager.initialize(t.websocket),n=new Promise((a,o)=>setTimeout(()=>o(new Error("Extended WebSocket connection timeout")),45e3));await Promise.race([s,n]),this.wsConnected=!0,this.connectionState="connected",e=!0,this.setupMessageHandlers(),console.log("✅ WebSocket connection established successfully"),this.setupLateConnectionHandler();try{const a=this.testBackendAPI(),o=new Promise((l,d)=>setTimeout(()=>d(new Error("API test timeout")),5e3));await Promise.race([a,o]),console.log("✅ Backend API endpoints verified")}catch(a){const o=a instanceof Error?a.message:"Unknown error";console.warn("⚠️ WebSocket connected but API endpoints unavailable:",o),console.warn("⚠️ Continuing with WebSocket-only mode - chat functionality will work")}}catch(s){i=s,this.wsConnected=!1,this.connectionState="disconnected",e=!1;const n=s instanceof Error?s.message:"Unknown error";console.error("❌ WebSocket connection failed:",n),this.setupLateConnectionHandler()}e?this.configService.isProductionMode()?this.initializeProductionMode():this.initializeDebugMode():(console.warn("⚠️ Backend server not available, initializing demo mode"),console.warn("WebSocket error details:",i?.message||"Unknown error"),this.initializeDemoMode())}catch(t){console.error("Failed to initialize app:",t),this.error="Failed to initialize application"}finally{this.isLoading=!1}}setupLateConnectionHandler(){const t=e=>{e.detail&&!this.wsConnected&&(console.log("🔄 Late WebSocket connection detected - recovering from demo mode"),this.wsConnected=!0,this.connectionState="connected",this.configService.isDebugMode()?this.initializeDebugMode():this.initializeProductionMode(),this.updateChatConnectionStatus("connected"),window.removeEventListener("websocket-connected",t))};window.addEventListener("websocket-connected",t),setTimeout(()=>{window.removeEventListener("websocket-connected",t)},3e4)}async testBackendAPI(){const t=this.getBackendBaseUrl();try{if((await fetch(`${t}/api/health/`,{method:"GET",signal:AbortSignal.timeout(3e3)})).ok){console.log("✅ Health endpoint check passed");return}}catch(i){const s=i instanceof Error?i.message:"Unknown error";console.warn("⚠️ Health endpoint check failed, trying fallback:",s)}const e=await fetch(`${t}/api/debug/users/`,{method:"HEAD",signal:AbortSignal.timeout(3e3)});if(!e.ok)throw new Error(`API test failed: ${e.status} ${e.statusText}`)}getBackendBaseUrl(){let e=this.configService.getConfig().websocket.url;return e.startsWith("ws://")?e=e.replace("ws://","http://"):e.startsWith("wss://")&&(e=e.replace("wss://","https://")),e=e.replace("/ws/game/",""),e}initializeProductionMode(){console.log("🚀 Initializing production mode with backend connection"),this.messages=[{id:"prod-welcome",type:"system",content:"🎯 Welcome to Goali! I'm your personal life coach. Tell me what's on your mind and I'll help you find the perfect activity.",timestamp:new Date}],this.wheelData=null,this.error=null,this.updateChatConnectionStatus("connected"),console.log("✅ Production mode initialized")}initializeDebugMode(){console.log("🐛 Initializing debug mode with backend connection");const t=this.configService.getConfig();this.messages=[{id:"debug-welcome",type:"system",content:"🐛 Debug mode active. Backend connected. Use the debug panel (Ctrl+Shift+D) to configure settings, or start chatting to generate a wheel.",timestamp:new Date}],t.debug.mockDataEnabled?(console.log("🎭 Mock data enabled in debug mode - creating demo wheel"),this.wheelData={segments:[{id:"1",text:"Go for a 20-minute walk",percentage:15,color:"#FF6B6B"},{id:"2",text:"Call a friend or family member",percentage:12,color:"#4ECDC4"},{id:"3",text:"Read 10 pages of a book",percentage:18,color:"#45B7D1"},{id:"4",text:"Do 5 minutes of meditation",percentage:10,color:"#96CEB4"},{id:"5",text:"Write in a journal",percentage:8,color:"#FFEAA7"},{id:"6",text:"Learn something new online",percentage:15,color:"#DDA0DD"},{id:"7",text:"Organize one small area",percentage:12,color:"#98D8C8"},{id:"8",text:"Practice a hobby or skill",percentage:10,color:"#F7DC6F"}],wheelId:"debug-demo-wheel-001",createdAt:new Date().toISOString()},this.messages.push({id:"debug-demo-wheel",type:"ai",content:"🎡 Demo wheel loaded! This is a sample wheel with various activities. In debug mode with mock data enabled, you can test the wheel functionality.",timestamp:new Date})):this.wheelData=null,this.error=null,this.updateChatConnectionStatus("connected"),console.log("✅ Debug mode initialized")}updateChatConnectionStatus(t){const e=this.shadowRoot?.querySelector("chat-interface");e?(e.connectionStatus=t,console.log(`🔄 Chat interface connection status updated to: ${t}`)):console.warn("⚠️ Chat interface not found for status update")}initializeDemoMode(){console.log("🎭 Initializing demo mode (backend unavailable)");const t=this.configService.getConfig();this.configService.isProductionMode()||t.debug.mockDataEnabled?(this.wheelData={segments:[{id:"1",text:"Go for a 20-minute walk",percentage:15,color:"#FF6B6B"},{id:"2",text:"Call a friend or family member",percentage:12,color:"#4ECDC4"},{id:"3",text:"Read 10 pages of a book",percentage:18,color:"#45B7D1"},{id:"4",text:"Do 5 minutes of meditation",percentage:10,color:"#96CEB4"},{id:"5",text:"Write in a journal",percentage:8,color:"#FFEAA7"},{id:"6",text:"Learn something new online",percentage:15,color:"#DDA0DD"},{id:"7",text:"Organize one small area",percentage:12,color:"#98D8C8"},{id:"8",text:"Practice a hobby or skill",percentage:10,color:"#F7DC6F"}],wheelId:"demo-wheel-001",createdAt:new Date().toISOString()},this.messages=[{id:"demo-1",type:"system",content:"🎯 Welcome to Goali! This is demo mode - your personalized activity wheel is ready.",timestamp:new Date},{id:"demo-2",type:"ai",content:"Hi! I've created a sample wheel with various activities to help you get started. Give it a spin to choose your next adventure! In the full version, I would create personalized activities based on our conversation.",timestamp:new Date}]):(this.wheelData=null,this.messages=[{id:"debug-welcome",type:"system",content:"🐛 Debug mode active. Use the debug panel (Ctrl+Shift+D) to configure settings, or start chatting to generate a wheel.",timestamp:new Date}]),this.error=null,console.log(`✅ Demo mode initialized (${this.configService.getMode()} mode, wheel: ${this.wheelData?"enabled":"disabled"})`)}setupMessageHandlers(){this.debugFilter.setDebugMode(this.configService.isDebugMode()),this.websocketManager.onMessage("wheel_data",t=>{this.handleWheelGenerated(t)}),this.websocketManager.onMessage("chat_message",t=>{this.handleAIResponse(t)}),this.websocketManager.onMessage("system_message",t=>{this.handleSystemMessage(t)}),this.websocketManager.onMessage("error",t=>{this.handleError(t)}),this.websocketManager.onMessage("debug_info",t=>{if(this.debugFilter.shouldShowDebugMessage(t)){let e="Unknown debug message";if(t.content&&typeof t.content=="object"?t.content.message?e=t.content.message:t.content.details?.message?e=t.content.details.message:t.content.source&&t.content.level?e=`[${t.content.source}] ${t.content.message||t.content.level}`:e=JSON.stringify(t.content,null,2):t.content&&typeof t.content=="string"?e=t.content:t.message?e=t.message:e=JSON.stringify(t,null,2),!e||e==="undefined"||e.includes("undefined")){const i=t.content?.source||"unknown",s=t.content?.level||"info",n=t.content?.details?JSON.stringify(t.content.details):"no details";e=`[${i}:${s}] ${n}`}console.log("🔧 Debug:",e)}}),this.websocketManager.onMessage("workflow_status",t=>{this.configService.isDebugMode()&&console.log("🔄 Workflow status:",t)}),this.websocketManager.onMessage("processing_status",t=>{this.isLoading=t.status==="processing",this.configService.isDebugMode()&&console.log("⚙️ Processing status:",t.status)}),this.websocketManager.onMessage("progress_update",t=>{this.handleProgressUpdate(t)}),this.websocketManager.onMessage("performance_metrics",t=>{this.handlePerformanceMetrics(t)}),this.websocketManager.onMessage("workflow_progress",t=>{this.handleWorkflowProgress(t)}),this.websocketManager.onConnectionChange(t=>{this.wsConnected=t,this.connectionState=t?"connected":"disconnected",console.log(`🔄 Connection state updated: ${this.connectionState}`)})}handleWheelGenerated(t){try{if(console.log("🎡 Received wheel data:",t),t.wheel&&t.wheel.items&&Array.isArray(t.wheel.items)){this.wheelData={segments:t.wheel.items.map(i=>({id:i.id,text:i.title||i.name||"Activity",percentage:i.percentage,color:i.color,description:i.description||""})),wheelId:`wheel-${Date.now()}`,createdAt:new Date().toISOString()},console.log("✅ Wheel data processed successfully:",this.wheelData);const e={id:`msg-${Date.now()}`,type:"system",content:"Your personalized activity wheel has been generated! Give it a spin to choose your next adventure.",timestamp:new Date};this.messages=[...this.messages,e],this.requestUpdate()}else console.error("❌ Invalid wheel data structure:",t),this.handleError({content:"Invalid wheel data received from server"})}catch(e){console.error("❌ Error processing wheel data:",e),this.handleError({content:"Failed to process wheel data"})}}handleSystemMessage(t){const e={id:`msg-${Date.now()}`,type:"system",content:t.content||"System message",timestamp:new Date};this.messages=[...this.messages,e],this.requestUpdate()}handleAIResponse(t){if(t.is_user)return;const e={id:`msg-${Date.now()}`,type:"ai",content:t.content||"No content",timestamp:new Date,metadata:{processingTime:t.processingTime}};this.messages=[...this.messages,e],this.requestUpdate()}handleError(t){this.error=t.content||t.message||"An error occurred";const e={id:`msg-${Date.now()}`,type:"error",content:t.content||t.message||"An error occurred",timestamp:new Date,metadata:{error:{code:t.code||"UNKNOWN",details:t.details||""}}};this.messages=[...this.messages,e]}handleProgressUpdate(t){this.configService.isDebugMode()&&console.log("📊 Progress update:",t),!this.showProgressBar&&t.data&&(this.showProgressBar=!0,this.currentProgressTrackerId=t.data.tracker_id,this.progressTitle=t.data.workflow_type==="wheel_generation"?"Generating Wheel...":"Processing...");const e=this.shadowRoot?.querySelector("real-time-progress-bar");e&&e.dispatchEvent(new CustomEvent("progress-update",{detail:t})),t.data?.stage==="completed"&&t.data?.progress_percent>=100&&setTimeout(()=>{this.showProgressBar=!1,this.currentProgressTrackerId=""},2e3)}handlePerformanceMetrics(t){this.configService.isDebugMode()&&console.log("⚡ Performance metrics:",t);const e=this.shadowRoot?.querySelector("real-time-progress-bar");e&&e.dispatchEvent(new CustomEvent("performance-metrics",{detail:t}))}handleWorkflowProgress(t){this.configService.isDebugMode()&&console.log("🔄 Workflow progress:",t),!this.showProgressBar&&t.data&&(this.showProgressBar=!0,this.progressTitle=`${t.data.workflow_type?.replace("_"," ").toUpperCase()||"Processing"}...`);const e=this.shadowRoot?.querySelector("real-time-progress-bar");e&&e.dispatchEvent(new CustomEvent("workflow-progress",{detail:t})),t.data?.overall_progress>=100&&setTimeout(()=>{this.showProgressBar=!1},2e3)}loadDebugSelections(){if(this.configService.isDebugMode())try{this.debugUserId=localStorage.getItem("debug_selected_user_id")||"",this.debugLLMConfigId=localStorage.getItem("debug_selected_llm_config_id")||""}catch(t){console.warn("Failed to load debug selections:",t)}}checkForActiveEnjoyOverlay(){try{const t=localStorage.getItem("goali_last_wheel_spin");if(t){const e=JSON.parse(t),s=Date.now()-e.timestamp,n=10*60*1e3;if(s<n){console.log("🎯 Restoring enjoy overlay from previous session"),this.enjoyOverlayActivity=e.activity,this.enjoyOverlayStartTime=e.timestamp,this.showEnjoyOverlay=!0;const a=n-s;setTimeout(()=>{this.transitionToFeedbackModal()},a)}else console.log("🎯 Showing post-activity feedback modal from previous session"),this.showPostActivityFeedbackWithActivity(e.activity)}}catch(t){console.warn("Failed to check for active enjoy overlay:",t),localStorage.removeItem("goali_last_wheel_spin")}}showEnjoyOverlayWithActivity(t){const e=Date.now(),i={timestamp:e,activity:{id:t.id,name:t.name,description:t.description,domain:t.domain,type:t.type,icon:t.icon,base_challenge_rating:t.base_challenge_rating,duration_range:t.duration_range,instructions:t.instructions,requirements:t.requirements,materials:t.materials}};try{localStorage.setItem("goali_last_wheel_spin",JSON.stringify(i))}catch(s){console.warn("Failed to store wheel spin data:",s)}this.enjoyOverlayActivity=t,this.enjoyOverlayStartTime=e,this.showEnjoyOverlay=!0,setTimeout(()=>{this.transitionToFeedbackModal()},10*60*1e3)}transitionToFeedbackModal(){this.showEnjoyOverlay&&this.enjoyOverlayActivity&&(this.showEnjoyOverlay=!1,this.showPostActivityFeedbackWithActivity(this.enjoyOverlayActivity))}showPostActivityFeedbackWithActivity(t){const i={timestamp:Date.now(),activity:{id:t.id,name:t.name,description:t.description,domain:t.domain,type:t.type,icon:t.icon,base_challenge_rating:t.base_challenge_rating,duration_range:t.duration_range,instructions:t.instructions,requirements:t.requirements,materials:t.materials}};try{localStorage.setItem("goali_last_wheel_spin",JSON.stringify(i))}catch(s){console.warn("Failed to store wheel spin data:",s)}this.postActivityFeedbackConfig={title:"How was your experience?",message:`Tell us about your experience with "${t.name}". Your feedback helps us improve the system and provide better activity recommendations.`,feedback_type:"post_activity_feedback",content_type:"WheelItem",object_id:t.id,activity:t},this.showPostActivityFeedbackModal=!0}hideEnjoyOverlay(){this.showEnjoyOverlay=!1,this.enjoyOverlayActivity=null,this.enjoyOverlayStartTime=null;try{localStorage.removeItem("goali_last_wheel_spin")}catch(t){console.warn("Failed to clean up wheel spin data:",t)}}resetAppFromEnjoyOverlay(){this.hideEnjoyOverlay(),this.wheelData=null,this.messages=[],this.error=null,this.isLoading=!1,this.showActivityModal=!1,this.showCreateActivityModal=!1,this.showProfileModal=!1,this.showWinningModal=!1,this.showFeedbackModal=!1,this.showAddActivityModal=!1,this.showContractModal=!1,this.showPostActivityFeedbackModal=!1,this.winningActivity=null,this.requestUpdate(),console.log("🔄 App reset from enjoy overlay")}resetAppAfterFeedback(){this.wheelData=null,this.messages=[],this.error=null,this.isLoading=!1,this.showActivityModal=!1,this.showCreateActivityModal=!1,this.showProfileModal=!1,this.showWinningModal=!1,this.showFeedbackModal=!1,this.showAddActivityModal=!1,this.showContractModal=!1,this.showEnjoyOverlay=!1,this.showPostActivityFeedbackModal=!1,this.winningActivity=null,this.enjoyOverlayActivity=null,this.enjoyOverlayStartTime=null,this.requestUpdate(),console.log("🔄 App reset after post-activity feedback")}loadCurrentUser(){const t=this.currentUser?.id;if(this.configService.isDebugMode()&&this.debugUserId)this.currentUser={id:this.debugUserId,name:`Debug User ${this.debugUserId}`,isStaff:!0};else{const e=this.authService.getCurrentUser();e?this.currentUser={id:e.id,name:e.name||e.username||`User ${e.id}`,isStaff:e.is_staff||!1}:this.currentUser=null}t!==this.currentUser?.id&&this.invalidateActivityCache()}getButtonText(){if(this.isLoading)return"Generating...";switch(this.connectionState){case"disconnected":return"Connecting...";case"connecting":return"Connecting...";case"connected":return"Generate";default:return"Generate"}}getConnectionStatusText(){switch(this.connectionState){case"disconnected":return"Disconnected";case"connecting":return"Connecting...";case"connected":return"Connected";default:return"Unknown"}}openUserProfile(){this.showProfileModal=!0,this.loadCurrentUserProfile()}async loadCurrentUserProfile(){try{console.log("🔄 Loading user profile data from API...");const e=this.configService.getConfig().websocket.url.replace("ws://","http://").replace("wss://","https://").replace("/ws/game/",""),i=this.debugUserId||"2",s=`${e}/api/user/profile/${i}/`,n=await fetch(s,{method:"GET",headers:{"Content-Type":"application/json",...this.authService.getToken()?{Authorization:`Bearer ${this.authService.getToken()}`}:{}}});if(n.ok){const a=await n.json();if(a.success&&a.profile)this.currentUserProfile={id:a.profile.id,name:a.profile.name,description:a.profile.description,demographics:a.profile.demographics,environment:a.profile.environment,environments:a.profile.environments,preferences:a.profile.preferences,goals:a.profile.goals,is_real:a.profile.is_real},console.log("✅ User profile loaded successfully:",this.currentUserProfile);else throw new Error(a.error||"Failed to load profile data")}else console.warn("⚠️ Profile API failed, using fallback data"),this.currentUserProfile={id:i,name:this.currentUser?.name||"PhiPhi",description:"Profile data not available",demographics:{age:"Not set",location:"Not set",occupation:"Not set"},environment:{living_situation:"Not set",available_resources:"Not set",constraints:"Not set"},goals:{short_term:"Not set",long_term:"Not set",motivation:"Not set"}}}catch(t){console.error("❌ Failed to load user profile:",t),this.currentUserProfile={id:this.debugUserId||"2",name:this.currentUser?.name||"PhiPhi",description:"Profile data not available",demographics:{age:"Not set",location:"Not set",occupation:"Not set"},environment:{living_situation:"Not set",available_resources:"Not set",constraints:"Not set"},goals:{short_term:"Not set",long_term:"Not set",motivation:"Not set"}}}}async handleMessageSend(t){const{message:e}=t.detail;if(this.messages=[...this.messages,e],this.wsConnected){let i;this.configService.isDebugMode()&&this.debugUserId?i=this.debugUserId:i=this.authService.getCurrentUser()?.id||"2",console.log("Sending message with debug selections:",{userId:i,llmConfigId:this.debugLLMConfigId});try{await this.messageHandler.sendChatMessage(e.content,i,void 0,this.configService.isDebugMode()?this.debugLLMConfigId:void 0)}catch(s){console.error("Failed to send message:",s),this.websocketManager.sendMessage("chat_message",{message:e.content,user_profile_id:i,timestamp:e.timestamp.toISOString(),metadata:this.configService.isDebugMode()&&this.debugLLMConfigId?{llm_config_id:this.debugLLMConfigId}:{}})}}else this.simulateAIResponse(e.content)}simulateAIResponse(t){setTimeout(()=>{const e=["That's interesting! In the full version, I would analyze your message and provide personalized guidance. For now, try spinning the wheel to explore the sample activities!","I understand! While this is demo mode, you can still experience the core wheel functionality. Each activity is designed to be quick and rewarding.","Great question! In the complete version, I would create custom activities based on your interests, goals, and current situation. The wheel you see contains some popular starter activities.","Thanks for sharing! The wheel spinning experience you're about to try is the same as in the full version - realistic physics and engaging interactions await!"],i=e[Math.floor(Math.random()*e.length)],s={id:`demo-ai-${Date.now()}`,type:"ai",content:i,timestamp:new Date,metadata:{processingTime:800+Math.random()*400}};this.messages=[...this.messages,s]},1e3+Math.random()*1e3)}handleWheelSpinComplete(t){console.log("🎯 [APP-SHELL] handleWheelSpinComplete called!",t.detail);const{winningSegment:e,finalAngle:i,duration:s}=t.detail;if(e){console.log("🎯 [APP-SHELL] Processing winning segment:",e);const n=this.wheelData?.segments?.find(o=>o.id===e.activityId||o.id===e.id||o.activity_tailored_id===e.activityId||o.activity_tailored_id===e.id);console.log("🎯 [APP-SHELL] Found original activity:",n),this.winningActivity={id:e.id||e.activityId,name:e.text||e.name,color:e.color,percentage:e.percentage,description:n?.description||n?.tailored_description||"No description available",instructions:n?.instructions||n?.tailored_instructions||"",domain:n?.domain||"general",base_challenge_rating:n?.base_challenge_rating||n?.challenge_rating||50,duration_range:n?.duration_range||n?.estimated_duration||"Flexible",type:n?.type||(n?.activity_tailored_id?"tailored":"generic"),activity_tailored_id:n?.activity_tailored_id||e.activityId,wheel_item_id:e.id||e.activityId,spinDuration:s,finalAngle:i,requirements:n?.requirements||n?.social_requirements,materials:n?.materials,icon:n?.icon||(n?.type==="tailored"?"⭐":"🎯")},console.log("🎯 [APP-SHELL] Complete winning activity data:",this.winningActivity),setTimeout(()=>{this.showWinningModal=!0,this.requestUpdate()},2e3);const a={id:`msg-${Date.now()}`,type:"wheel-result",content:`🎯 You spun: ${e.text}`,timestamp:new Date,metadata:{wheelResult:{segmentId:e.id,segmentText:e.text,spinDuration:s}}};if(this.messages=[...this.messages,a],this.wsConnected){const o=this.wheelData?.segments?.find(l=>l.id===e.activityId);console.log("🎯 [APP-SHELL] Sending spin_result to backend:",{activity_tailored_id:e.activityId||e.id,name:e.text,description:o?.description||e.text,user_profile_id:"2"}),this.websocketManager.sendMessage("spin_result",{activity_tailored_id:e.activityId||e.id,name:e.text,description:o?.description||e.text,user_profile_id:"2"})}else setTimeout(()=>{const o={id:`demo-followup-${Date.now()}`,type:"ai",content:`Great choice! "${e.text}" is a wonderful activity. In the full version, I would provide personalized tips and track your progress. For now, enjoy exploring this activity!`,timestamp:new Date};this.messages=[...this.messages,o]},2e3)}}handleLoginSuccess(){this.isAuthenticated=!0,this.initializeApp()}handleDemoModeRequest(){this.isAuthenticated=!0,this.initializeDemoMode()}async handleLogout(){try{this.isAuthenticated=!1,this.currentUser=null,this.wsConnected=!1,this.connectionState="disconnected",this.wheelData=null,this.messages=[],this.error=null,this.showDebugPanel=!1,this.showActivityModal=!1,this.showCreateActivityModal=!1,this.showProfileModal=!1,this.showWinningModal=!1,this.websocketManager.disconnect(),this.requestUpdate(),await this.authService.logout(),setTimeout(()=>{window.location.reload()},50),console.log("✅ User logged out successfully")}catch(t){console.error("❌ Logout failed:",t),this.isAuthenticated=!1,this.currentUser=null,setTimeout(()=>{window.location.reload()},50)}}handleClearError(){this.error=null}getTimeInMinutes(){return Math.round(10+this.timeAvailable/100*230)}formatTimeAvailable(){const t=this.getTimeInMinutes();if(t<60)return`${t}min`;{const e=Math.floor(t/60),i=t%60;return i===0?`${e}h`:`${e}h ${i}min`}}performWheelSpin(){console.log("[APP] Performing wheel spin...");const t=this.shadowRoot?.querySelector(".wheel-foreground game-wheel");if(t&&typeof t.spin=="function"){console.log("[APP] Found interactive wheel component, checking state...");const e=t.getWheelState();if(console.log("[APP] Wheel state:",e),!e.hasPhysicsEngine||!e.hasRenderer){console.log("[APP] Wheel not fully initialized, waiting and retrying..."),setTimeout(()=>{console.log("[APP] Retrying spin after initialization delay..."),this.performWheelSpin()},500);return}if(e.isSpinning){console.log("[APP] Wheel is already spinning, ignoring spin request");return}console.log("[APP] Triggering wheel spin..."),t.spin()}else{console.error("[APP] Interactive wheel component not found or spin method not available"),console.log("[APP] Available wheel components:",this.shadowRoot?.querySelectorAll("game-wheel"));const e=this.shadowRoot?.querySelector("game-wheel");e&&(console.log("[APP] Found fallback wheel component, attempting spin..."),e.spin())}}async performSearch(){if(!this.searchQuery.trim()){await this.loadActivityCatalog();return}try{console.log(`🔍 Searching activities for: "${this.searchQuery}"`),this.isLoading=!0;const t=await fetch(`${this.getBackendBaseUrl()}/api/activities/catalog/?search=${encodeURIComponent(this.searchQuery)}&limit=50`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.authService.getToken()}`}});if(t.ok){const e=await t.json();e.success&&e.activities?(this.activityCatalog=e.activities,console.log(`✅ Search found ${e.total_count} activities`)):console.warn("⚠️ Invalid search response")}else console.warn("⚠️ Search request failed")}catch(t){console.error("❌ Error performing search:",t)}finally{this.isLoading=!1}}renderEditableField(t,e,i){return this.editingProfileFields.has(t)?r`
        <div class="profile-field">
          <label>${e}</label>
          <input
            type="text"
            class="profile-edit-input"
            .value=${i}
            @keydown=${n=>{n.key==="Enter"?this.saveProfileField(t,n.target.value):n.key==="Escape"&&this.stopEditingField(t)}}
            @blur=${n=>{this.saveProfileField(t,n.target.value)}}
            autofocus
          />
          <div class="profile-edit-actions">
            <button
              class="profile-edit-btn save"
              @click=${n=>{const a=n.target.parentElement?.previousElementSibling;this.saveProfileField(t,a.value)}}
            >
              Save
            </button>
            <button
              class="profile-edit-btn cancel"
              @click=${()=>this.stopEditingField(t)}
            >
              Cancel
            </button>
          </div>
        </div>
      `:r`
      <div class="profile-field">
        <label>${e}</label>
        <div
          class="profile-value editable"
          @click=${()=>this.startEditingField(t)}
          title="Click to edit"
        >
          ${i}
        </div>
      </div>
    `}async loadActivityCatalog(){const t=this.currentUser?.id||null,e=Date.now();if(this.activityCatalogCache.data&&this.activityCatalogCache.userId===t&&e-this.activityCatalogCache.timestamp<this.ACTIVITY_CACHE_TTL){console.log("✅ Using cached activity catalog"),this.activityCatalog=this.activityCatalogCache.data;return}try{console.log("🔄 Loading fresh activity catalog from API...");const s=this.configService.getConfig().websocket.url.replace("ws://","http://").replace("wss://","https://").replace("/ws/game/",""),n=await fetch(`${s}/api/activities/catalog/`,{method:"GET",headers:{"Content-Type":"application/json",...this.authService.getToken()?{Authorization:`Bearer ${this.authService.getToken()}`}:{}}});if(n.ok){const a=await n.json();a.success&&a.activities?(this.activityCatalog=a.activities,this.activityCatalogCache={data:a.activities,timestamp:e,userId:t},console.log(`✅ Loaded ${a.total_count} activities (${a.tailored_count} tailored, ${a.generic_count} generic)`)):(console.warn("⚠️ Invalid response from activity catalog API"),this.loadFallbackActivityCatalog())}else console.warn("⚠️ Failed to load activity catalog from API, using fallback"),this.loadFallbackActivityCatalog()}catch(i){console.error("❌ Error loading activity catalog:",i),this.loadFallbackActivityCatalog()}}invalidateActivityCache(){this.activityCatalogCache={data:null,timestamp:0,userId:null},console.log("🗑️ Activity catalog cache invalidated")}loadFallbackActivityCatalog(){this.activityCatalog=[{id:"cat-1",name:"Morning Yoga",description:"Start your day with gentle stretching and mindfulness",domain:"physical",base_challenge_rating:30,type:"generic",icon:"🎯"},{id:"cat-2",name:"Creative Writing",description:"Express yourself through words and storytelling",domain:"creative",base_challenge_rating:50,type:"generic",icon:"🎯"},{id:"cat-3",name:"Learn a New Recipe",description:"Explore culinary skills with a new dish",domain:"practical",base_challenge_rating:40,type:"generic",icon:"🎯"},{id:"cat-4",name:"Nature Photography",description:"Capture the beauty of the outdoors",domain:"creative",base_challenge_rating:60,type:"generic",icon:"🎯"},{id:"cat-5",name:"Meditation Session",description:"Find inner peace and clarity",domain:"mental",base_challenge_rating:25,type:"generic",icon:"🎯"}]}get filteredCatalog(){return this.activityCatalog.sort((e,i)=>e.type==="tailored"&&i.type==="generic"?-1:e.type==="generic"&&i.type==="tailored"?1:e.name.localeCompare(i.name))}getWheelItems(){return this.wheelData?.segments?this.wheelData.segments.map(t=>({id:t.id,name:t.name||t.text||"Unknown Activity",description:t.description||"No description available",percentage:t.percentage,color:t.color,domain:t.domain||"general",base_challenge_rating:t.base_challenge_rating||50,activity_tailored_id:t.activity_tailored_id||t.id})):[]}renderActivityList(){const t=this.getWheelItems();return t.length===0?"":r`
      <div class="activity-list">
        <div class="activity-list-header">
          <span>Activities (${t.length})</span>
          <button
            class="add-activity-btn"
            @click=${this.openAddActivityModal}
            title="Add activity to wheel"
          >
            +
          </button>
        </div>
        ${t.map(e=>this.renderActivityItem(e))}
      </div>
    `}renderActivityItem(t){const e=this.expandedActivities.has(t.id);return r`
      <div class="activity-item ${e?"expanded":""}">
        <div class="activity-item-header" @click=${()=>this.toggleActivityExpanded(t.id)}>
          <div class="activity-item-title">
            <div class="activity-color-dot" style="background-color: ${t.color}"></div>
            <span class="activity-name">${t.name}</span>
          </div>
          <div style="display: flex; align-items: center; gap: var(--spacing-2);">
            <button
              class="remove-activity-btn"
              @click=${i=>this.handleRemoveActivity(i,t)}
              title="Remove from wheel"
            >
              ❌
            </button>
            <div class="activity-expand-icon">▼</div>
          </div>
        </div>
        <div class="activity-item-content">
          <div class="activity-item-details">
            <div class="activity-description">${t.description}</div>
            <div class="activity-meta">
              <div class="activity-meta-item">
                <span>🎯</span>
                <span>Domain: ${t.domain}</span>
              </div>
              <div class="activity-meta-item">
                <span>⚡</span>
                <span>Challenge: ${t.base_challenge_rating}/100</span>
              </div>
              <div class="activity-meta-item">
                <span>📊</span>
                <span>Weight: ${t.percentage.toFixed(1)}%</span>
              </div>
            </div>

          </div>
        </div>
      </div>
    `}renderActivityModal(){return this.showActivityModal?r`
      <div class="modal-overlay" @click=${this.closeActivityModal}>
        <div class="modal activity-modal" @click=${t=>t.stopPropagation()}>
          <div class="modal-header">
            <h3 class="modal-title">Change Activity</h3>
            <button class="modal-close" @click=${this.closeActivityModal}>×</button>
          </div>
          <div class="modal-body">
            <button class="create-activity-btn" @click=${this.openCreateActivityModal}>
              <span>➕</span>
              <span>Create New Activity</span>
            </button>
            <input
              type="text"
              class="search-input"
              placeholder="Search activities..."
              .value=${this.searchQuery}
              @input=${this.handleSearchChange}
            />
            <div class="activity-catalog">
              ${this.isLoading?r`
                <div class="loading-state">
                  <div class="spinner"></div>
                  <p>Creating tailored activity...</p>
                </div>
              `:this.filteredCatalog.length>0?this.filteredCatalog.map(t=>r`
                  <div class="catalog-item ${t.type}" @click=${()=>this.selectCatalogActivity(t)}>
                    <div class="catalog-item-info">
                      <div class="catalog-item-header">
                        <span class="activity-type-icon ${t.type}">${t.type==="tailored"?"⭐":"🎯"}</span>
                        <div class="catalog-item-name">${t.name}</div>
                        <span class="activity-type-badge ${t.type}">${t.type==="tailored"?"Tailored":"Generic"}</span>
                      </div>
                      <div class="catalog-item-description">${t.description}</div>
                      <div class="catalog-item-meta">
                        <span>🎯 ${t.domain}</span>
                        <span>⚡ ${t.base_challenge_rating}/100</span>
                      </div>
                    </div>
                  </div>
                `):r`<div class="no-results">No activities found matching "${this.searchQuery}"</div>`}
            </div>
          </div>
        </div>
      </div>
    `:""}renderCreateActivityModal(){return this.showCreateActivityModal?r`
      <div class="modal-overlay" @click=${this.closeCreateActivityModal}>
        <div class="modal" @click=${t=>t.stopPropagation()}>
          <div class="modal-header">
            <h3 class="modal-title">Create New Activity</h3>
            <button class="modal-close" @click=${this.closeCreateActivityModal}>×</button>
          </div>
          <div class="modal-body">
            <form @submit=${t=>{t.preventDefault(),this.createNewActivity()}}>
              <div class="form-group">
                <label class="form-label" for="activity-name">Activity Name *</label>
                <input
                  type="text"
                  id="activity-name"
                  class="form-input"
                  placeholder="e.g., Morning Yoga, Creative Writing..."
                  .value=${this.newActivityForm.name}
                  @input=${t=>this.handleNewActivityFormChange("name",t.target.value)}
                  required
                />
              </div>

              <div class="form-group">
                <label class="form-label" for="activity-description">Description *</label>
                <textarea
                  id="activity-description"
                  class="form-textarea"
                  placeholder="Describe what this activity involves and why it's beneficial..."
                  .value=${this.newActivityForm.description}
                  @input=${t=>this.handleNewActivityFormChange("description",t.target.value)}
                  required
                ></textarea>
              </div>

              <div class="form-group">
                <label class="form-label" for="activity-domain">Domain</label>
                <select
                  id="activity-domain"
                  class="form-select"
                  .value=${this.newActivityForm.domain}
                  @change=${t=>this.handleNewActivityFormChange("domain",t.target.value)}
                >
                  <option value="general">General</option>
                  <option value="physical">Physical</option>
                  <option value="mental">Mental</option>
                  <option value="creative">Creative</option>
                  <option value="social">Social</option>
                  <option value="practical">Practical</option>
                  <option value="learning">Learning</option>
                  <option value="relaxation">Relaxation</option>
                </select>
              </div>

              <div class="form-group">
                <label class="form-label" for="activity-challenge">Challenge Level</label>
                <input
                  type="range"
                  id="activity-challenge"
                  class="form-range"
                  min="1"
                  max="100"
                  .value=${this.newActivityForm.base_challenge_rating.toString()}
                  @input=${t=>this.handleNewActivityFormChange("base_challenge_rating",parseInt(t.target.value))}
                />
                <div class="range-value">${this.newActivityForm.base_challenge_rating}/100</div>
              </div>

              <div class="modal-actions">
                <button type="button" class="btn-secondary" @click=${this.closeCreateActivityModal}>
                  Cancel
                </button>
                <button
                  type="submit"
                  class="btn-primary"
                  ?disabled=${!this.newActivityForm.name.trim()||!this.newActivityForm.description.trim()}
                >
                  Create Activity
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    `:""}render(){return this.isAuthenticated?r`
      <div class="app-container">
        <!-- Header -->
        <header class="header">
          <div class="header-content">
            <div class="logo">
              <div class="logo-icon">🎯</div>
              <span>Goali</span>
              ${this.configService.isDebugMode()?r`
                <span style="font-size: 0.7em; color: #ffaa00; margin-left: 8px;">DEBUG</span>
              `:""}
            </div>
            <div class="status-bar">
              <!-- Connection Status -->
              <div class="connection-status">
                <div class="status-indicator ${this.connectionState==="connected"?"connected":this.connectionState==="connecting"?"connecting":"disconnected"}"></div>
                <span>${this.getConnectionStatusText()}</span>
              </div>

              <!-- User Info -->
              ${this.currentUser?r`
                <div class="user-info">
                  <span class="user-name">${this.currentUser.name}</span>
                  ${this.currentUser.isStaff?r`<span class="staff-badge">Staff</span>`:""}
                  <button
                    class="user-icon-btn"
                    @click=${this.openUserProfile}
                    title="User Profile Management"
                  >
                    👤
                  </button>
                  <button
                    class="logout-btn"
                    @click=${this.handleLogout}
                    title="Logout"
                  >
                    🚪
                  </button>
                </div>
              `:""}

              <!-- Debug Controls (only for staff users) -->
              ${this.currentUser?.isStaff?r`
                <button
                  class="debug-btn"
                  @click=${()=>this.showDebugPanel=!this.showDebugPanel}
                >
                  🐛 Debug
                </button>
              `:""}
            </div>
          </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
          <!-- Error Banner -->
          ${this.error?r`
            <div class="error-banner">
              <span>⚠️ ${this.error}</span>
              <button @click=${this.handleClearError}>Dismiss</button>
            </div>
          `:""}

          <!-- Real-Time Progress Bar -->
          ${this.showProgressBar?r`
            <div class="progress-bar-container">
              <real-time-progress-bar
                .trackerId=${this.currentProgressTrackerId}
                .workflowType=${"wheel_generation"}
                .title=${this.progressTitle}
                .showMetrics=${!0}
                .showTimeline=${!1}
                .compact=${!1}
              ></real-time-progress-bar>
            </div>
          `:""}

          <!-- Wheel Section -->
          <section class="wheel-section">
            <div class="wheel-container">
              <!-- Background wheel (always visible, greyed out) -->
              <div class="wheel-background">
                <game-wheel
                  .wheelData=${this.getBackgroundWheelData()}
                  hide-ui
                  disable-interaction
                ></game-wheel>
              </div>

              <!-- Foreground content -->
              <div class="wheel-foreground">
                ${this.wheelData?r`
                  <game-wheel
                    .wheelData=${this.wheelData}
                    @wheel-spin-complete=${this.handleWheelSpinComplete}
                  ></game-wheel>
                `:r`
                  <div class="wheel-placeholder">
                    <div class="wheel-placeholder-icon">🎡</div>
                    <h3>Your Personalized Activity Wheel</h3>
                    <p>
                      ${this.wsConnected?"Adjust your availability and energy level, and I'll create a custom wheel of activities just for you!":this.configService.isDebugMode()?"Debug mode: Use the chat below to generate your wheel, or configure settings with the debug panel (Ctrl+Shift+D).":"Connect to start creating your personalized activity wheel!"}
                    </p>
                    ${this.wsConnected?r`
                      <div style="margin-top: 16px; font-size: 14px; color: #666; font-style: italic;">
                        💡
                      </div>
                    `:""}
                  </div>
                `}
              </div>
            </div>

            <!-- Wheel Controls -->
            <div class="wheel-controls">
              <!-- Button Bar -->
              <div class="button-bar">
                <div class="potentiometer-control">
                  <label class="potentiometer-label">Time Available</label>
                  <input
                    type="range"
                    min="0"
                    max="100"
                    .value=${this.timeAvailable.toString()}
                    @input=${this.handleTimeAvailableChange}
                    class="potentiometer-slider"
                  />
                  <div class="potentiometer-value">${this.formatTimeAvailable()}</div>
                </div>

                <!-- Generate/Spin Button -->
                <div class="action-button-container">
                  ${this.wheelData?r`
                    <button
                      class="action-button spin-button"
                      ?disabled=${this.isLoading||this.connectionState!=="connected"}
                      @click=${this.handleSpinWheel}
                    >
                      ${this.isLoading?"Spinning...":"SPIN!"}
                    </button>
                  `:r`
                    <button
                      class="action-button generate-button"
                      ?disabled=${this.connectionState!=="connected"||this.isLoading}
                      @click=${this.handleGenerateWheel}
                    >
                      ${this.getButtonText()}
                    </button>
                  `}
                </div>

                <div class="potentiometer-control">
                  <label class="potentiometer-label">Energy Level</label>
                  <input
                    type="range"
                    min="0"
                    max="100"
                    .value=${this.energyLevel.toString()}
                    @input=${this.handleEnergyLevelChange}
                    class="potentiometer-slider"
                  />
                  <div class="potentiometer-value">${this.energyLevel}%</div>
                </div>
              </div>

              <!-- Activity List -->
              ${this.wheelData?this.renderActivityList():""}
            </div>
          </section>

          <!-- Chat Section (hidden for wheel focus) -->
          <!--
          <section class="chat-section">
            <chat-interface
              .messages=${this.messages}
              .connectionStatus=${this.wsConnected?"connected":"disconnected"}
              .isProcessing=${this.isLoading}
              @message-send=${this.handleMessageSend}
            ></chat-interface>
          </section>
          -->
        </main>

        <!-- Connection Error Banner -->
        ${!this.wsConnected&&this.connectionState==="disconnected"?r`
          <div class="error-banner connection-error-banner">
            <span>⚠️ Impossible to connect to backend</span>
          </div>
        `:""}

        <!-- Debug Panel -->
        ${this.configService.isDebugMode()?r`
          <debug-panel
            .visible=${this.showDebugPanel}
            @user-changed=${this.handleDebugUserChange}
            @llm-config-changed=${this.handleDebugLLMConfigChange}
            @backend-url-changed=${this.handleDebugBackendUrlChange}
          ></debug-panel>
        `:""}

        <!-- Contract Disclaimer Modal -->
        <contract-disclaimer-modal
          ?visible=${this.showContractModal}
          @contract-accepted=${this.handleContractAccepted}
          @contract-cancelled=${this.handleContractCancelled}
        ></contract-disclaimer-modal>

        <!-- Activity Change Modal -->
        ${this.renderActivityModal()}

        <!-- Create Activity Modal -->
        ${this.renderCreateActivityModal()}

        <!-- Profile Modal -->
        ${this.renderProfileModal()}

        <!-- Winning Modal -->
        ${this.renderWinningModal()}

        <!-- Feedback Modal -->
        ${this.renderFeedbackModal()}

        <!-- Add Activity Modal -->
        ${this.renderAddActivityModal()}

        <!-- Enjoy Overlay -->
        ${this.renderEnjoyOverlay()}

        <!-- Post-Activity Feedback Modal -->
        ${this.renderPostActivityFeedbackModal()}
      </div>
    `:r`
        <login-form
          @login-success=${this.handleLoginSuccess}
          .hideDemoMode=${!0}
        ></login-form>
      `}handleDebugUserChange(t){console.log("Debug: User changed to",t.detail.userId),this.debugUserId=t.detail.userId,this.loadCurrentUser()}handleDebugLLMConfigChange(t){console.log("Debug: LLM config changed to",t.detail.configId),this.debugLLMConfigId=t.detail.configId}handleDebugBackendUrlChange(t){console.log("Debug: Backend URL changed to",t.detail.url),this.websocketManager.disconnect(),setTimeout(()=>{this.initializeApp()},1e3)}renderProfileModal(){return this.showProfileModal?r`
      <div class="modal-overlay profile-modal-overlay" @click=${this.closeProfileModal}>
        <div class="modal profile-modal" @click=${t=>t.stopPropagation()}>
          <div class="modal-header">
            <h3>👤 User Profile</h3>
            <button class="modal-close" @click=${this.closeProfileModal}>×</button>
          </div>

          <div class="modal-body">
            ${this.currentUserProfile?r`
              <!-- Basic Information & Demographics - Compact Display -->
              <div class="profile-compact-section">
                <div class="profile-compact-header">
                  <h4>
                    <span class="profile-section-icon">👤</span>
                    Basic Information & Demographics
                  </h4>
                </div>
                <div class="profile-compact-grid">
                  <div class="profile-field-compact">
                    <label>Full Name</label>
                    <div class="profile-value-compact">${this.currentUserProfile.demographics?.full_name||this.currentUserProfile.name||"Not set"}</div>
                  </div>
                  <div class="profile-field-compact">
                    <label>Age</label>
                    <div class="profile-value-compact">${this.currentUserProfile.demographics?.age||"Not set"}</div>
                  </div>
                  <div class="profile-field-compact">
                    <label>Gender</label>
                    <div class="profile-value-compact">${this.currentUserProfile.demographics?.gender||"Not set"}</div>
                  </div>
                  <div class="profile-field-compact">
                    <label>Location</label>
                    <div class="profile-value-compact">${this.currentUserProfile.demographics?.location||"Not set"}</div>
                  </div>
                  <div class="profile-field-compact">
                    <label>Language</label>
                    <div class="profile-value-compact">${this.currentUserProfile.demographics?.language||"Not set"}</div>
                  </div>
                  <div class="profile-field-compact">
                    <label>Occupation</label>
                    <div class="profile-value-compact">${this.currentUserProfile.demographics?.occupation||"Not set"}</div>
                  </div>
                  <div class="profile-field-compact profile-field-full-width">
                    <label>Profile Description</label>
                    <div class="profile-value-compact">${this.currentUserProfile.description||"Not set"}</div>
                  </div>
                </div>
              </div>

              <!-- Goals Section -->
              <div class="profile-section ${this.expandedProfileSections.has("goals")?"expanded":""}">
                <div class="profile-section-header" @click=${()=>this.toggleProfileSection("goals")}>
                  <h4>
                    <span class="profile-section-icon">🎯</span>
                    Goals & Aspirations
                  </h4>
                  <span class="profile-section-toggle">▼</span>
                </div>
                <div class="profile-section-content">
                  <div class="profile-section-body">
                    ${this.currentUserProfile.goals&&Array.isArray(this.currentUserProfile.goals)&&this.currentUserProfile.goals.length>0?this.currentUserProfile.goals.map(t=>r`
                        <div class="profile-field">
                          <label>${t.title} (${t.goal_type})</label>
                          <div class="profile-value">
                            <div class="goal-description">${t.description}</div>
                            <div class="goal-meta">
                              <span class="goal-importance">Importance: ${t.importance_according_user}/100</span>
                              <span class="goal-strength">Strength: ${t.strength}/100</span>
                            </div>
                          </div>
                        </div>
                      `):r`
                        <div class="profile-field">
                          <label>Goals & Aspirations</label>
                          <div class="profile-value">No goals defined yet</div>
                        </div>
                      `}
                  </div>
                </div>
              </div>

              <!-- Environment Section -->
              <div class="profile-section ${this.expandedProfileSections.has("environment")?"expanded":""}">
                <div class="profile-section-header" @click=${()=>this.toggleProfileSection("environment")}>
                  <h4>
                    <span class="profile-section-icon">🏠</span>
                    Environment & Context
                  </h4>
                  <span class="profile-section-toggle">▼</span>
                </div>
                <div class="profile-section-content">
                  <div class="profile-section-body">
                    ${this.currentUserProfile.environment?r`
                      <div class="profile-field">
                        <label>Current Environment</label>
                        <div class="profile-value">${this.currentUserProfile.environment.environment_name||"Not set"}</div>
                      </div>
                      <div class="profile-field">
                        <label>Environment Description</label>
                        <div class="profile-value">${this.currentUserProfile.environment.environment_description||"Not set"}</div>
                      </div>
                      ${this.currentUserProfile.environment.environment_details?r`
                        <div class="profile-field">
                          <label>Environment Details</label>
                          <div class="profile-value environment-details">
                            ${Object.entries(this.currentUserProfile.environment.environment_details).map(([t,e])=>r`
                              <div class="detail-item">
                                <span class="detail-key">${t.replace(/_/g," ").replace(/\b\w/g,i=>i.toUpperCase())}:</span>
                                <span class="detail-value">${e}</span>
                              </div>
                            `)}
                          </div>
                        </div>
                      `:""}
                    `:r`
                      <div class="profile-field">
                        <label>Environment & Context</label>
                        <div class="profile-value">No environment information available</div>
                      </div>
                    `}
                  </div>
                </div>
              </div>
            `:r`
              <div class="loading-state">
                <div class="spinner"></div>
                <p>Loading profile...</p>
              </div>
            `}
          </div>

          <div class="modal-footer">
            <button class="btn btn-secondary" @click=${this.closeProfileModal}>
              Close
            </button>
            <button class="btn btn-primary" @click=${this.openFullProfileEditor}>
              Edit Profile
            </button>
          </div>
        </div>
      </div>
    `:""}renderEnjoyOverlay(){if(!this.showEnjoyOverlay||!this.enjoyOverlayActivity)return"";const t=Date.now(),e=this.enjoyOverlayStartTime||t,i=t-e,s=10*60*1e3,n=Math.max(0,s-i),a=Math.ceil(n/(60*1e3));return r`
      <div class="enjoy-overlay">
        <div class="enjoy-overlay-content">
          <h1 class="enjoy-title">Enjoy!</h1>

          <div class="enjoy-activity-info">
            <div class="enjoy-activity-name">
              ${this.enjoyOverlayActivity.icon||"🎯"} ${this.enjoyOverlayActivity.name}
            </div>
            <div class="enjoy-activity-description">
              ${this.enjoyOverlayActivity.description||"No description available"}
            </div>
          </div>

          <div class="enjoy-activity-details">
            <div class="enjoy-detail-item">
              <div class="enjoy-detail-label">Domain</div>
              <div class="enjoy-detail-value">${this.enjoyOverlayActivity.domain||"General"}</div>
            </div>
            <div class="enjoy-detail-item">
              <div class="enjoy-detail-label">Challenge</div>
              <div class="enjoy-detail-value">${this.enjoyOverlayActivity.base_challenge_rating||"N/A"}/100</div>
            </div>
            <div class="enjoy-detail-item">
              <div class="enjoy-detail-label">Duration</div>
              <div class="enjoy-detail-value">${this.enjoyOverlayActivity.duration_range||"Flexible"}</div>
            </div>
            <div class="enjoy-detail-item">
              <div class="enjoy-detail-label">Type</div>
              <div class="enjoy-detail-value">${this.enjoyOverlayActivity.type==="tailored"?"✨ Tailored":"📋 Generic"}</div>
            </div>
          </div>

          ${this.enjoyOverlayActivity.instructions?r`
            <div class="enjoy-activity-info">
              <div class="enjoy-detail-label">📝 Instructions:</div>
              <div class="enjoy-activity-description">${this.enjoyOverlayActivity.instructions}</div>
            </div>
          `:""}

          ${this.enjoyOverlayActivity.requirements?r`
            <div class="enjoy-activity-info">
              <div class="enjoy-detail-label">📋 Requirements:</div>
              <div class="enjoy-activity-description">${this.enjoyOverlayActivity.requirements}</div>
            </div>
          `:""}

          ${this.enjoyOverlayActivity.materials?r`
            <div class="enjoy-activity-info">
              <div class="enjoy-detail-label">🛠️ Materials:</div>
              <div class="enjoy-activity-description">${this.enjoyOverlayActivity.materials}</div>
            </div>
          `:""}

          <div class="enjoy-hourglass">⏳</div>

          <div class="enjoy-timer">
            ${a>0?`${a} minute${a!==1?"s":""} remaining`:"Time's up!"}
          </div>

          <button class="enjoy-close-btn" @click=${this.resetAppFromEnjoyOverlay}>
            Reset App
          </button>
        </div>
      </div>
    `}renderPostActivityFeedbackModal(){if(!this.showPostActivityFeedbackModal)return"";const t=this.postActivityFeedbackConfig.activity;return t?r`
      <div class="modal-overlay" @click=${this.closePostActivityFeedbackModal}>
        <div class="modal post-activity-feedback-modal" @click=${e=>e.stopPropagation()}>
          <div class="modal-header">
            <h3 class="modal-title">${this.postActivityFeedbackConfig.title}</h3>
            <button class="modal-close" @click=${this.closePostActivityFeedbackModal}>×</button>
          </div>
          <div class="modal-body">
            <div class="activity-summary">
              <div class="activity-info">
                <div class="activity-name">
                  ${t.icon||"🎯"} ${t.name}
                </div>
                <div class="activity-description">
                  ${t.description||"No description available"}
                </div>
              </div>
            </div>

            <p class="feedback-message">${this.postActivityFeedbackConfig.message}</p>

            <div class="feedback-form">
              <!-- Experience Rating -->
              <div class="form-group rating-group">
                <label class="form-label">How was your overall experience?</label>
                <div class="rating-options">
                  <label class="rating-option">
                    <input type="radio" name="experience-rating" value="1">
                    <span class="rating-text">😞 Poor</span>
                  </label>
                  <label class="rating-option">
                    <input type="radio" name="experience-rating" value="2">
                    <span class="rating-text">😐 Fair</span>
                  </label>
                  <label class="rating-option">
                    <input type="radio" name="experience-rating" value="3">
                    <span class="rating-text">🙂 Good</span>
                  </label>
                  <label class="rating-option">
                    <input type="radio" name="experience-rating" value="4">
                    <span class="rating-text">😊 Great</span>
                  </label>
                  <label class="rating-option">
                    <input type="radio" name="experience-rating" value="5">
                    <span class="rating-text">🤩 Excellent</span>
                  </label>
                </div>
              </div>

              <!-- Difficulty Rating -->
              <div class="form-group rating-group">
                <label class="form-label">How challenging was this activity?</label>
                <div class="rating-options">
                  <label class="rating-option">
                    <input type="radio" name="difficulty-rating" value="1">
                    <span class="rating-text">😴 Too Easy</span>
                  </label>
                  <label class="rating-option">
                    <input type="radio" name="difficulty-rating" value="2">
                    <span class="rating-text">😌 Easy</span>
                  </label>
                  <label class="rating-option">
                    <input type="radio" name="difficulty-rating" value="3">
                    <span class="rating-text">😊 Just Right</span>
                  </label>
                  <label class="rating-option">
                    <input type="radio" name="difficulty-rating" value="4">
                    <span class="rating-text">😅 Challenging</span>
                  </label>
                  <label class="rating-option">
                    <input type="radio" name="difficulty-rating" value="5">
                    <span class="rating-text">😰 Too Hard</span>
                  </label>
                </div>
              </div>

              <!-- Enjoyment Rating -->
              <div class="form-group rating-group">
                <label class="form-label">How much did you enjoy it?</label>
                <div class="rating-options">
                  <label class="rating-option">
                    <input type="radio" name="enjoyment-rating" value="1">
                    <span class="rating-text">😒 Boring</span>
                  </label>
                  <label class="rating-option">
                    <input type="radio" name="enjoyment-rating" value="2">
                    <span class="rating-text">😑 Meh</span>
                  </label>
                  <label class="rating-option">
                    <input type="radio" name="enjoyment-rating" value="3">
                    <span class="rating-text">🙂 Okay</span>
                  </label>
                  <label class="rating-option">
                    <input type="radio" name="enjoyment-rating" value="4">
                    <span class="rating-text">😄 Fun</span>
                  </label>
                  <label class="rating-option">
                    <input type="radio" name="enjoyment-rating" value="5">
                    <span class="rating-text">🥳 Amazing</span>
                  </label>
                </div>
              </div>

              <!-- Recommendation Rating -->
              <div class="form-group rating-group">
                <label class="form-label">Would you recommend this activity to others?</label>
                <div class="rating-options">
                  <label class="rating-option">
                    <input type="radio" name="recommendation-rating" value="1">
                    <span class="rating-text">👎 No</span>
                  </label>
                  <label class="rating-option">
                    <input type="radio" name="recommendation-rating" value="2">
                    <span class="rating-text">🤷 Maybe</span>
                  </label>
                  <label class="rating-option">
                    <input type="radio" name="recommendation-rating" value="3">
                    <span class="rating-text">👍 Yes</span>
                  </label>
                  <label class="rating-option">
                    <input type="radio" name="recommendation-rating" value="4">
                    <span class="rating-text">💯 Definitely</span>
                  </label>
                  <label class="rating-option">
                    <input type="radio" name="recommendation-rating" value="5">
                    <span class="rating-text">🌟 Absolutely</span>
                  </label>
                </div>
              </div>

              <!-- Additional Comments -->
              <div class="form-group">
                <label class="form-label" for="post-activity-feedback-comment">Additional thoughts or suggestions:</label>
                <textarea
                  id="post-activity-feedback-comment"
                  class="form-textarea"
                  placeholder="Share any additional thoughts, what you learned, or suggestions for improvement..."
                  rows="4"
                ></textarea>
              </div>
            </div>

            <div class="modal-actions">
              <button type="button" class="btn-secondary" @click=${this.closePostActivityFeedbackModal}>
                Skip Feedback
              </button>
              <button type="button" class="btn-primary" @click=${this.submitPostActivityFeedback}>
                Submit Feedback
              </button>
            </div>
          </div>
        </div>
      </div>
    `:""}renderWinningModal(){if(!this.showWinningModal||!this.winningActivity)return"";const t=z.getStoredSignature();return r`
      <div class="modal-overlay winning-modal-overlay" @click=${this.closeWinningModal}>
        <div class="modal winning-modal-content" @click=${e=>e.stopPropagation()}>
          <div class="winning-header">
            <div class="winning-icon">🎉</div>
            <h2>Congratulations!</h2>
            <button class="modal-close" @click=${this.closeWinningModal}>×</button>
          </div>

          ${t?r`
            <div class="contract-confirmation">
              <div class="contract-confirmation-header">
                <span class="contract-icon">⚖️</span>
                <span class="contract-message">You have approved this choice</span>
              </div>
              <div class="signature-display">
                <img
                  src="${t.imageData}"
                  alt="Your signature"
                  class="signature-image"
                />
                <div class="signature-timestamp">
                  Signed: ${new Date(t.timestamp).toLocaleString()}
                </div>
              </div>
            </div>
          `:""}

          <div class="modal-body">
            <div class="winning-activity">
              <!-- Activity Header -->
              <div class="activity-header">
                <div class="activity-icon">${this.winningActivity.icon||"🎯"}</div>
                <div class="activity-title">
                  <div class="activity-name">${this.winningActivity.name||this.winningActivity.text}</div>
                  <div class="activity-type-badge ${this.winningActivity.type||"generic"}">
                    ${this.winningActivity.type==="tailored"?"✨ Tailored":"📋 Generic"}
                  </div>
                </div>
              </div>

              <!-- Activity Description -->
              <div class="activity-description">
                ${this.winningActivity.description||"No description available"}
              </div>

              <!-- Activity Instructions (if available) -->
              ${this.winningActivity.instructions?r`
                <div class="activity-instructions">
                  <h4>📝 Instructions:</h4>
                  <p>${this.winningActivity.instructions}</p>
                </div>
              `:""}

              <!-- Tailored Information (if available) -->
              ${this.winningActivity.tailored_description&&this.winningActivity.tailored_description!==this.winningActivity.description?r`
                <div class="tailored-info">
                  <h4>🎯 Personalized for You:</h4>
                  <p>${this.winningActivity.tailored_description}</p>
                </div>
              `:""}

              <!-- Activity Details Grid -->
              <div class="activity-details">
                <div class="detail-item">
                  <span class="detail-label">🏷️ Domain:</span>
                  <span class="detail-value">${this.winningActivity.domain||"General"}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">⚡ Challenge:</span>
                  <span class="detail-value">${this.winningActivity.base_challenge_rating||"N/A"}/100</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">⏱️ Duration:</span>
                  <span class="detail-value">${this.winningActivity.duration_range||"Flexible"}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">🎯 Type:</span>
                  <span class="detail-value">${this.winningActivity.type==="tailored"?"✨ Tailored":"📋 Generic"}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">📊 Weight:</span>
                  <span class="detail-value">${this.winningActivity.percentage?this.winningActivity.percentage.toFixed(1)+"%":"N/A"}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">🎲 Spin Time:</span>
                  <span class="detail-value">${this.winningActivity.spinDuration?(this.winningActivity.spinDuration/1e3).toFixed(1)+"s":"N/A"}</span>
                </div>
              </div>

              <!-- Additional Information -->
              ${this.winningActivity.requirements||this.winningActivity.materials?r`
                <div class="activity-requirements">
                  ${this.winningActivity.requirements?r`
                    <div class="requirement-section">
                      <h4>📋 Requirements:</h4>
                      <p>${this.winningActivity.requirements}</p>
                    </div>
                  `:""}
                  ${this.winningActivity.materials?r`
                    <div class="requirement-section">
                      <h4>🛠️ Materials:</h4>
                      <p>${this.winningActivity.materials}</p>
                    </div>
                  `:""}
                </div>
              `:""}

              <!-- Action Button -->
              <div class="winning-actions">
                <button class="primary-button" @click=${this.closeWinningModal}>
                  Let's Do This! 🚀
                </button>
              </div>
            </div>
          </div>

          <div class="modal-footer">
            <button class="btn btn-primary" @click=${this.closeWinningModal}>
              Let's Do It! 🚀
            </button>
            <button class="btn btn-secondary" @click=${this.closeWinningModal}>
              Maybe Later
            </button>
          </div>
        </div>
      </div>
    `}renderFeedbackModal(){return this.showFeedbackModal?r`
      <div class="modal-overlay" @click=${this.closeFeedbackModal}>
        <div class="modal" @click=${t=>t.stopPropagation()}>
          <div class="modal-header">
            <h3 class="modal-title">${this.feedbackModalConfig.title}</h3>
            <button class="modal-close" @click=${this.closeFeedbackModal}>×</button>
          </div>
          <div class="modal-body">
            <p>${this.feedbackModalConfig.message}</p>
            <div class="form-group">
              <label class="form-label" for="feedback-comment">Your feedback:</label>
              <textarea
                id="feedback-comment"
                class="form-textarea"
                placeholder="Tell us why you don't want this activity..."
                rows="4"
              ></textarea>
            </div>
            <div class="modal-actions">
              <button type="button" class="btn-secondary" @click=${this.closeFeedbackModal}>
                Cancel
              </button>
              <button type="button" class="btn-primary" @click=${this.submitFeedback}>
                Send Feedback
              </button>
            </div>
          </div>
        </div>
      </div>
    `:""}renderAddActivityModal(){return this.showAddActivityModal?r`
      <div class="modal-overlay" @click=${this.closeAddActivityModal}>
        <div class="modal activity-modal" @click=${t=>t.stopPropagation()}>
          <div class="modal-header">
            <h3 class="modal-title">Add Activity to Wheel</h3>
            <button class="modal-close" @click=${this.closeAddActivityModal}>×</button>
          </div>
          <div class="modal-body">
            <input
              type="text"
              class="search-input"
              placeholder="Search activities..."
              .value=${this.searchQuery}
              @input=${this.handleSearchChange}
            />
            <div class="activity-catalog">
              ${this.isLoading?r`
                <div class="loading-state">
                  <div class="spinner"></div>
                  <p>Loading activities...</p>
                </div>
              `:this.filteredCatalog.length>0?this.filteredCatalog.map(t=>r`
                  <div class="catalog-item ${t.type}" @click=${()=>this.addActivityToWheel(t)}>
                    <div class="catalog-item-info">
                      <div class="catalog-item-header">
                        <span class="activity-type-icon ${t.type}">${t.type==="tailored"?"⭐":"🎯"}</span>
                        <div class="catalog-item-name">${t.name}</div>
                        <span class="activity-type-badge ${t.type}">${t.type==="tailored"?"Tailored":"Generic"}</span>
                      </div>
                      <div class="catalog-item-description">${t.description}</div>
                      <div class="catalog-item-meta">
                        <span>🎯 ${t.domain}</span>
                        <span>⚡ ${t.base_challenge_rating}/100</span>
                      </div>
                    </div>
                  </div>
                `):r`<div class="no-results">No activities found matching "${this.searchQuery}"</div>`}
            </div>
          </div>
        </div>
      </div>
    `:""}getBackgroundWheelData(){return{segments:[{id:"bg-1",text:"🏃‍♂️ Exercise",percentage:20,color:"#FF6B6B",description:"Physical activity and movement"},{id:"bg-2",text:"📚 Learning",percentage:20,color:"#4ECDC4",description:"Study and skill development"},{id:"bg-3",text:"🎨 Creative",percentage:20,color:"#45B7D1",description:"Artistic and creative pursuits"},{id:"bg-4",text:"🧘‍♀️ Mindfulness",percentage:20,color:"#96CEB4",description:"Meditation and relaxation"},{id:"bg-5",text:"👥 Social",percentage:20,color:"#FFEAA7",description:"Connect with friends and family"}]}}};u.styles=O`
    :host {
      display: block;
      width: 100%;
      min-height: 100vh;
      background: var(--gradient-primary);
      position: relative;
      overflow-x: hidden;
    }

    .app-container {
      display: flex;
      flex-direction: column;
      min-height: 100vh;
      max-width: 100vw;
      margin: 0 auto;
      position: relative;
    }

    .header {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
      padding: var(--spacing-4) var(--spacing-6);
      position: sticky;
      top: 0;
      z-index: var(--z-sticky);
    }

    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      max-width: 1200px;
      margin: 0 auto;
    }

    .logo {
      display: flex;
      align-items: center;
      gap: var(--spacing-3);
      color: white;
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-bold);
    }

    .logo-icon {
      width: 32px;
      height: 32px;
      border-radius: var(--radius-full);
      background: rgba(255, 255, 255, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .status-bar {
      display: flex;
      align-items: center;
      gap: var(--spacing-4);
      color: white;
      font-size: var(--font-size-sm);
    }

    .connection-status {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
    }

    .status-indicator {
      width: 8px;
      height: 8px;
      border-radius: var(--radius-full);
      background: var(--color-error);
      transition: background var(--transition-fast);
    }

    .status-indicator.connected {
      background: var(--color-success);
      box-shadow: 0 0 8px rgba(76, 175, 80, 0.4);
    }

    .status-indicator.connecting {
      background: #ff9800;
      box-shadow: 0 0 8px rgba(255, 152, 0, 0.4);
      animation: pulse 1.5s ease-in-out infinite;
    }

    .status-indicator.disconnected {
      background: var(--color-error);
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }

    .user-info {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
    }

    .user-name {
      font-weight: 500;
    }

    .staff-badge {
      background: rgba(255, 193, 7, 0.2);
      color: #ffc107;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 10px;
      font-weight: 600;
      text-transform: uppercase;
      border: 1px solid rgba(255, 193, 7, 0.3);
    }

    .user-icon-btn, .logout-btn {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s ease;
      font-size: 14px;
    }

    .user-icon-btn:hover, .logout-btn:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.3);
    }

    .logout-btn:hover {
      background: rgba(255, 107, 107, 0.3);
      border-color: rgba(255, 107, 107, 0.5);
    }

    .debug-btn {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      padding: 4px 8px;
      font-size: 10px;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .debug-btn:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    .main-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: var(--spacing-6);
      gap: var(--spacing-6);
      max-width: 1200px;
      margin: 0 auto;
      width: 100%;
      position: relative;
      z-index: 1;
    }

    .wheel-section {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--spacing-6);
      min-height: 60vh;
    }

    .wheel-controls {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--spacing-4);
      width: 100%;
      max-width: 500px;
    }

    .button-bar {
      display: flex;
      gap: var(--spacing-4);
      align-items: center;
      justify-content: center;
      width: 100%;
      padding: var(--spacing-4);
      background: rgba(255, 255, 255, 0.05);
      border-radius: 12px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .potentiometer-control {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--spacing-2);
      flex: 1;
    }

    .potentiometer-label {
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
      font-weight: 500;
      text-align: center;
    }

    .potentiometer-slider {
      width: 100%;
      max-width: 120px;
      height: 6px;
      border-radius: 3px;
      background: linear-gradient(to right, #4CAF50, #FFC107, #FF5722);
      outline: none;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .potentiometer-slider::-webkit-slider-thumb {
      appearance: none;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: #fff;
      border: 2px solid #007bff;
      cursor: pointer;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      transition: all 0.2s ease;
    }

    .potentiometer-slider::-webkit-slider-thumb:hover {
      transform: scale(1.1);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    .potentiometer-slider::-moz-range-thumb {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: #fff;
      border: 2px solid #007bff;
      cursor: pointer;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }

    .potentiometer-value {
      font-size: var(--font-size-xs);
      color: var(--color-text-muted);
      font-weight: 600;
    }

    .action-button-container {
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 0 0 auto;
    }

    .action-button {
      padding: var(--spacing-3) var(--spacing-6);
      border: none;
      border-radius: 8px;
      font-size: var(--font-size-base);
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;
      min-width: 100px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .generate-button {
      background: linear-gradient(135deg, #4CAF50, #45a049);
      color: white;
      box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
    }

    .generate-button:hover:not(:disabled) {
      background: linear-gradient(135deg, #45a049, #3d8b40);
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4);
    }

    .generate-button:disabled {
      background: #666;
      cursor: not-allowed;
      opacity: 0.6;
      transform: none;
      box-shadow: none;
    }

    .spin-button {
      background: linear-gradient(135deg, #FF6B6B, #e55a5a);
      color: white;
      box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
    }

    .spin-button:hover:not(:disabled) {
      background: linear-gradient(135deg, #e55a5a, #d94545);
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(255, 107, 107, 0.4);
    }

    .spin-button:disabled {
      background: #666;
      cursor: not-allowed;
      opacity: 0.6;
      transform: none;
      box-shadow: none;
    }

    .activity-list {
      width: 100%;
      max-width: 500px;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 12px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      overflow: hidden;
    }

    .activity-list-header {
      padding: var(--spacing-4);
      background: rgba(255, 255, 255, 0.1);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      font-weight: 600;
      color: var(--color-text-primary);
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .add-activity-btn {
      background: var(--color-success);
      color: white;
      border: none;
      border-radius: 50%;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      font-size: 18px;
      font-weight: bold;
    }

    .add-activity-btn:hover {
      background: var(--color-success-dark);
      transform: scale(1.1);
    }

    .remove-activity-btn {
      background: none;
      border: none;
      color: var(--color-error);
      cursor: pointer;
      padding: var(--spacing-1);
      border-radius: 4px;
      transition: all 0.2s ease;
      font-size: 16px;
      opacity: 0.7;
    }

    .remove-activity-btn:hover {
      background: rgba(255, 107, 107, 0.1);
      opacity: 1;
      transform: scale(1.1);
    }

    .activity-item {
      border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    }

    .activity-item:last-child {
      border-bottom: none;
    }

    .activity-item-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--spacing-3) var(--spacing-4);
      cursor: pointer;
      transition: background-color 0.2s ease;
    }

    .activity-item-header:hover {
      background: rgba(255, 255, 255, 0.05);
    }

    .activity-item-title {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      flex: 1;
    }

    .activity-color-dot {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      flex-shrink: 0;
    }

    .activity-name {
      font-weight: 500;
      color: var(--color-text-primary);
    }

    .activity-expand-icon {
      transition: transform 0.2s ease;
      color: var(--color-text-secondary);
    }

    .activity-item.expanded .activity-expand-icon {
      transform: rotate(180deg);
    }

    .activity-item-content {
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease;
    }

    .activity-item.expanded .activity-item-content {
      max-height: 200px;
    }

    .activity-item-details {
      padding: 0 var(--spacing-4) var(--spacing-3) var(--spacing-4);
      border-top: 1px solid rgba(255, 255, 255, 0.05);
    }

    .activity-description {
      color: var(--color-text-secondary);
      font-size: var(--font-size-sm);
      line-height: 1.5;
      margin-bottom: var(--spacing-3);
    }

    .activity-meta {
      display: flex;
      gap: var(--spacing-4);
      margin-bottom: var(--spacing-3);
      font-size: var(--font-size-xs);
    }

    .activity-meta-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-1);
      color: var(--color-text-muted);
    }

    .activity-actions {
      display: flex;
      justify-content: flex-end;
    }

    .activity-change-btn {
      display: flex;
      align-items: center;
      gap: var(--spacing-1);
      padding: var(--spacing-2) var(--spacing-3);
      background: var(--color-primary);
      color: white;
      border: none;
      border-radius: 6px;
      font-size: var(--font-size-xs);
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .activity-change-btn:hover {
      background: var(--color-primary-dark);
      transform: translateY(-1px);
    }

    /* Modal styles */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      backdrop-filter: blur(4px);
    }

    .modal-overlay::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(255, 255, 255, 0.4);
      z-index: -1;
    }

    .profile-modal {
      max-width: 90vw;
      max-height: 90vh;
      overflow-y: auto;
    }

    /* Static Profile Sections */
    .profile-static-section {
      margin-bottom: var(--spacing-6);
      padding: var(--spacing-4);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: var(--radius-lg);
      background: rgba(255, 255, 255, 0.05);
    }

    .profile-static-section h4 {
      color: var(--color-primary);
      margin: 0 0 var(--spacing-4) 0;
      font-size: 1.1rem;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
    }

    .profile-static-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--spacing-3);
    }

    /* Compact Profile Section */
    .profile-compact-section {
      margin-bottom: var(--spacing-4);
      padding: var(--spacing-3);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: var(--radius-lg);
      background: rgba(255, 255, 255, 0.05);
    }

    .profile-compact-header h4 {
      color: var(--color-primary);
      margin: 0 0 var(--spacing-3) 0;
      font-size: 1rem;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
    }

    .profile-compact-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: var(--spacing-2);
    }

    .profile-field-compact {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-1);
    }

    .profile-field-compact.profile-field-full-width {
      grid-column: 1 / -1;
    }

    .profile-field-compact label {
      font-size: 0.8rem;
      font-weight: 500;
      color: var(--color-text-secondary);
      margin: 0;
    }

    .profile-value-compact {
      background: rgba(255, 255, 255, 0.03);
      padding: var(--spacing-1) var(--spacing-2);
      border-radius: var(--radius-sm);
      border: 1px solid rgba(255, 255, 255, 0.05);
      color: var(--color-text);
      font-size: 0.9rem;
      min-height: 1.5rem;
      display: flex;
      align-items: center;
    }

    /* Goal display styles */
    .goal-description {
      margin-bottom: var(--spacing-2);
      font-style: italic;
      color: var(--color-text-secondary);
    }

    .goal-meta {
      display: flex;
      gap: var(--spacing-3);
      font-size: 0.8rem;
      color: var(--color-text-muted);
    }

    .goal-importance, .goal-strength {
      padding: var(--spacing-1) var(--spacing-2);
      background: rgba(255, 255, 255, 0.05);
      border-radius: var(--radius-sm);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    /* Environment details styles */
    .environment-details {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-1);
    }

    .detail-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--spacing-1) var(--spacing-2);
      background: rgba(255, 255, 255, 0.02);
      border-radius: var(--radius-sm);
      font-size: 0.85rem;
    }

    .detail-key {
      font-weight: 500;
      color: var(--color-text-secondary);
      flex: 1;
    }

    .detail-value {
      color: var(--color-text);
      text-align: right;
      max-width: 60%;
      word-break: break-word;
    }

    /* Enhanced Profile Modal with Accordion */
    .profile-section {
      margin-bottom: var(--spacing-4);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: var(--radius-lg);
      overflow: hidden;
      background: rgba(255, 255, 255, 0.02);
    }

    .profile-section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--spacing-4);
      cursor: pointer;
      background: rgba(255, 255, 255, 0.05);
      transition: background-color 0.2s ease;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .profile-section-header:hover {
      background: rgba(255, 255, 255, 0.08);
    }

    .profile-section-header h4 {
      color: var(--color-primary);
      margin: 0;
      font-size: 1.1rem;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
    }

    .profile-section-icon {
      font-size: 1.2em;
    }

    .profile-section-toggle {
      transition: transform 0.2s ease;
      color: var(--color-text-secondary);
      font-size: 1.2em;
    }

    .profile-section.expanded .profile-section-toggle {
      transform: rotate(180deg);
    }

    .profile-section-content {
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease;
    }

    .profile-section.expanded .profile-section-content {
      max-height: 500px;
    }

    .profile-section-body {
      padding: var(--spacing-4);
    }

    .profile-field {
      margin-bottom: var(--spacing-3);
    }

    .profile-field label {
      display: block;
      font-weight: 500;
      color: var(--color-text-secondary);
      margin-bottom: var(--spacing-1);
      font-size: 0.9rem;
    }

    .profile-value {
      background: rgba(255, 255, 255, 0.05);
      padding: var(--spacing-2) var(--spacing-3);
      border-radius: var(--radius-md);
      border: 1px solid rgba(255, 255, 255, 0.1);
      color: var(--color-text);
    }

    .profile-value.editable {
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .profile-value.editable:hover {
      background: rgba(255, 255, 255, 0.08);
      border-color: var(--color-primary);
    }

    .profile-edit-input {
      width: 100%;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid var(--color-primary);
      color: var(--color-text);
      padding: var(--spacing-2) var(--spacing-3);
      border-radius: var(--radius-md);
      font-size: 0.9rem;
    }

    .profile-edit-input:focus {
      outline: none;
      box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.3);
    }

    .profile-edit-actions {
      display: flex;
      gap: var(--spacing-2);
      margin-top: var(--spacing-2);
    }

    .profile-edit-btn {
      padding: var(--spacing-1) var(--spacing-3);
      border: none;
      border-radius: var(--radius-sm);
      font-size: 0.8rem;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .profile-edit-btn.save {
      background: var(--color-success);
      color: white;
    }

    .profile-edit-btn.cancel {
      background: var(--color-error);
      color: white;
    }

    .loading-state {
      text-align: center;
      padding: var(--spacing-8);
    }

    .loading-state .spinner {
      width: 40px;
      height: 40px;
      border: 3px solid rgba(255, 255, 255, 0.1);
      border-top: 3px solid var(--color-primary);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto var(--spacing-4);
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .modal {
      background: var(--color-background);
      border-radius: 12px;
      width: 90%;
      max-width: 600px;
      max-height: 85vh;
      overflow: hidden;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
      border: 1px solid rgba(255, 255, 255, 0.1);
      display: flex;
      flex-direction: column;
    }

    /* Bootstrap-compatible modal classes */
    .modal-dialog {
      background: var(--color-background);
      border-radius: 12px;
      width: 90%;
      max-width: 600px;
      max-height: 85vh;
      overflow: hidden;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
      border: 1px solid rgba(255, 255, 255, 0.1);
      display: flex;
      flex-direction: column;
    }

    .modal-content {
      display: flex;
      flex-direction: column;
      height: 100%;
      background: transparent;
      border: none;
    }

    .modal-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--spacing-4);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      background: rgba(255, 255, 255, 0.05);
    }

    .modal-title {
      font-size: var(--font-size-lg);
      font-weight: 600;
      color: var(--color-text-primary);
    }

    .modal-close {
      background: none;
      border: none;
      font-size: var(--font-size-xl);
      color: var(--color-text-secondary);
      cursor: pointer;
      padding: var(--spacing-1);
      border-radius: 4px;
      transition: all 0.2s ease;
    }

    .modal-close:hover {
      background: rgba(255, 255, 255, 0.1);
      color: var(--color-text-primary);
    }

    .modal-body {
      padding: var(--spacing-4);
      flex: 1;
      overflow-y: auto;
      min-height: 0; /* Important for flex scrolling */
    }

    /* Enhanced scrolling for activity modal */
    .activity-modal .modal-body {
      max-height: calc(85vh - 120px); /* Account for header and footer */
      overflow-y: auto;
    }

    .search-input {
      width: 100%;
      padding: var(--spacing-3);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 8px;
      background: rgba(255, 255, 255, 0.05);
      color: var(--color-text-primary);
      font-size: var(--font-size-base);
      margin-bottom: var(--spacing-4);
    }

    .search-input:focus {
      outline: none;
      border-color: var(--color-primary);
      box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.2);
    }

    .search-input::placeholder {
      color: var(--color-text-muted);
    }

    .activity-catalog {
      display: grid;
      gap: var(--spacing-3);
      max-height: calc(60vh - 120px); /* Ensure scrolling within modal */
      overflow-y: auto;
      padding-right: var(--spacing-2); /* Space for scrollbar */
    }

    .catalog-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-3);
      padding: var(--spacing-3);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s ease;
      background: rgba(255, 255, 255, 0.02);
    }

    .catalog-item:hover {
      background: rgba(255, 255, 255, 0.05);
      border-color: var(--color-primary);
      transform: translateY(-1px);
    }

    .catalog-item.tailored {
      border-color: rgba(255, 193, 7, 0.3);
      background: rgba(255, 193, 7, 0.1);
    }

    .catalog-item.tailored:hover {
      border-color: rgba(255, 193, 7, 0.5);
      background: rgba(255, 193, 7, 0.15);
    }

    .catalog-item-info {
      flex: 1;
    }

    .catalog-item-name {
      font-weight: 500;
      color: var(--color-text-primary);
      margin-bottom: var(--spacing-1);
    }

    .catalog-item-description {
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
      line-height: 1.4;
    }

    .catalog-item-meta {
      display: flex;
      gap: var(--spacing-2);
      margin-top: var(--spacing-2);
      font-size: var(--font-size-xs);
      color: var(--color-text-muted);
    }

    .catalog-item-header {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      margin-bottom: var(--spacing-2);
    }

    .activity-type-icon {
      font-size: 1.2em;
    }

    .activity-type-badge {
      font-size: 0.75em;
      padding: 2px 6px;
      border-radius: 4px;
      font-weight: 600;
      text-transform: uppercase;
      margin-left: auto;
    }

    .activity-type-badge.tailored {
      background: rgba(255, 193, 7, 0.2);
      color: #ffc107;
      border: 1px solid rgba(255, 193, 7, 0.3);
    }

    .activity-type-badge.tailored::before {
      content: '✨ ';
      font-size: 0.9em;
    }

    .activity-type-badge.generic {
      background: rgba(108, 117, 125, 0.2);
      color: #6c757d;
      border: 1px solid rgba(108, 117, 125, 0.3);
    }

    .activity-type-badge.generic::before {
      content: '📋 ';
      font-size: 0.9em;
    }

    /* Enhanced visual differentiation for activity type icons */
    .activity-type-icon.tailored {
      color: #ffc107;
      text-shadow: 0 0 8px rgba(255, 193, 7, 0.5);
      font-size: 1.3em;
    }

    .activity-type-icon.generic {
      color: #6c757d;
      opacity: 0.8;
    }

    /* Enhanced catalog item styling for better differentiation */
    .catalog-item.tailored {
      border-left: 3px solid #ffc107;
      background: linear-gradient(135deg, rgba(255, 193, 7, 0.05), rgba(255, 193, 7, 0.02));
    }

    .catalog-item.generic {
      border-left: 3px solid rgba(108, 117, 125, 0.3);
      background: rgba(108, 117, 125, 0.02);
    }

    .catalog-item.tailored:hover {
      background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05));
      border-left-color: #ffcd39;
    }

    /* Winning Modal Styles */
    .winning-modal-overlay {
      background: rgba(0, 0, 0, 0.8);
      backdrop-filter: blur(5px);
    }

    .winning-modal-content {
      max-width: 500px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: var(--radius-xl);
      color: white;
      text-align: center;
      animation: winningModalAppear 0.5s ease-out;
    }

    @keyframes winningModalAppear {
      0% {
        transform: scale(0.8) translateY(-20px);
        opacity: 0;
      }
      100% {
        transform: scale(1) translateY(0);
        opacity: 1;
      }
    }

    .winning-header {
      padding: var(--spacing-6) var(--spacing-4) var(--spacing-4);
      position: relative;
    }

    .winning-icon {
      font-size: 3rem;
      margin-bottom: var(--spacing-2);
      animation: bounce 1s infinite;
    }

    @keyframes bounce {
      0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
      }
      40% {
        transform: translateY(-10px);
      }
      60% {
        transform: translateY(-5px);
      }
    }

    .winning-header h2 {
      margin: 0;
      font-size: 1.8rem;
      font-weight: 700;
    }

    .winning-activity {
      padding: var(--spacing-4);
    }

    .activity-header {
      display: flex;
      align-items: center;
      gap: var(--spacing-3);
      margin-bottom: var(--spacing-4);
    }

    .activity-icon {
      font-size: 2.5rem;
      text-align: center;
      min-width: 50px;
    }

    .activity-title {
      flex: 1;
    }

    .activity-name {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: var(--spacing-2);
      color: #fff;
    }

    .activity-description {
      font-size: 1rem;
      opacity: 0.9;
      margin-bottom: var(--spacing-4);
      line-height: 1.5;
      background: rgba(255, 255, 255, 0.1);
      padding: var(--spacing-3);
      border-radius: var(--radius-md);
      border-left: 4px solid rgba(255, 255, 255, 0.5);
    }

    .activity-instructions {
      background: rgba(255, 255, 255, 0.15);
      border-radius: var(--radius-md);
      padding: var(--spacing-4);
      margin-bottom: var(--spacing-4);
      text-align: left;
    }

    .activity-instructions h4 {
      margin: 0 0 var(--spacing-2) 0;
      color: rgba(255, 255, 255, 0.95);
      font-size: 1rem;
      font-weight: 600;
    }

    .activity-instructions p {
      margin: 0;
      color: rgba(255, 255, 255, 0.85);
      line-height: 1.5;
      font-size: 0.95rem;
    }

    .tailored-info {
      background: rgba(255, 193, 7, 0.2);
      border: 1px solid rgba(255, 193, 7, 0.4);
      border-radius: var(--radius-md);
      padding: var(--spacing-3);
      margin-bottom: var(--spacing-4);
    }

    .tailored-info h4 {
      color: #ffc107;
      margin: 0 0 var(--spacing-2) 0;
      font-size: 1rem;
    }

    .tailored-info p {
      margin: 0;
      color: #fff;
      line-height: 1.5;
      opacity: 0.9;
    }

    .activity-details {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-2);
      background: rgba(255, 255, 255, 0.1);
      padding: var(--spacing-3);
      border-radius: var(--radius-lg);
      margin-bottom: var(--spacing-4);
    }

    .detail-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--spacing-2);
      background: rgba(255, 255, 255, 0.05);
      border-radius: var(--radius-sm);
    }

    .detail-label {
      font-weight: 500;
      opacity: 0.8;
      font-size: 0.9rem;
    }

    .detail-value {
      font-weight: 600;
      font-size: 0.9rem;
    }

    .activity-requirements {
      margin-bottom: var(--spacing-4);
    }

    .requirement-section {
      margin-bottom: var(--spacing-3);
      background: rgba(255, 255, 255, 0.05);
      padding: var(--spacing-3);
      border-radius: var(--radius-md);
      border-left: 3px solid rgba(255, 255, 255, 0.3);
    }

    .requirement-section h4 {
      margin: 0 0 var(--spacing-2) 0;
      color: rgba(255, 255, 255, 0.9);
      font-size: 1rem;
    }

    .requirement-section p {
      margin: 0;
      color: rgba(255, 255, 255, 0.8);
      line-height: 1.5;
    }

    .winning-actions {
      text-align: center;
      margin-top: var(--spacing-4);
    }

    .winning-actions .primary-button {
      background: rgba(255, 255, 255, 0.9);
      color: #667eea;
      border: none;
      padding: var(--spacing-3) var(--spacing-6);
      border-radius: var(--radius-lg);
      font-size: 1.1rem;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .winning-actions .primary-button:hover {
      background: #fff;
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    }

    .winning-modal-content .modal-footer {
      padding: var(--spacing-4);
      display: flex;
      gap: var(--spacing-3);
      justify-content: center;
    }

    .winning-modal-content .btn {
      padding: var(--spacing-3) var(--spacing-6);
      border-radius: var(--radius-lg);
      font-weight: 600;
      transition: all 0.2s ease;
    }

    .winning-modal-content .btn-primary {
      background: #fff;
      color: #667eea;
      border: none;
    }

    .winning-modal-content .btn-primary:hover {
      background: #f8f9fa;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .winning-modal-content .btn-secondary {
      background: rgba(255, 255, 255, 0.2);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .winning-modal-content .btn-secondary:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
    }

    /* Contract Confirmation Styles in Winning Modal */
    .contract-confirmation {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 12px;
      padding: var(--spacing-4);
      margin-bottom: var(--spacing-4);
      backdrop-filter: blur(10px);
    }

    .contract-confirmation-header {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      margin-bottom: var(--spacing-3);
      font-weight: 600;
      color: rgba(255, 255, 255, 0.9);
    }

    .contract-icon {
      font-size: 1.2rem;
    }

    .contract-message {
      font-size: 0.95rem;
    }

    .signature-display {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--spacing-2);
    }

    .signature-image {
      max-width: 200px;
      max-height: 60px;
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 6px;
      background: rgba(255, 255, 255, 0.9);
      padding: var(--spacing-1);
    }

    .signature-timestamp {
      font-size: 0.8rem;
      color: rgba(255, 255, 255, 0.7);
      font-style: italic;
    }

    .no-results {
      text-align: center;
      padding: var(--spacing-6);
      color: var(--color-text-muted);
    }

    /* Create Activity Modal Styles */
    .create-activity-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-2);
      width: 100%;
      padding: var(--spacing-3);
      background: var(--color-primary);
      color: white;
      border: none;
      border-radius: 8px;
      font-size: var(--font-size-sm);
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      margin-bottom: var(--spacing-4);
    }

    .create-activity-btn:hover {
      background: var(--color-primary-dark);
      transform: translateY(-1px);
    }

    .form-group {
      margin-bottom: var(--spacing-4);
    }

    .form-label {
      display: block;
      margin-bottom: var(--spacing-2);
      font-weight: 500;
      color: var(--color-text-primary);
      font-size: var(--font-size-sm);
    }

    .form-input,
    .form-textarea,
    .form-select {
      width: 100%;
      padding: var(--spacing-3);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 6px;
      background: rgba(255, 255, 255, 0.05);
      color: var(--color-text-primary);
      font-size: var(--font-size-sm);
      transition: all 0.2s ease;
    }

    .form-input:focus,
    .form-textarea:focus,
    .form-select:focus {
      outline: none;
      border-color: var(--color-primary);
      box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.2);
    }

    .form-textarea {
      resize: vertical;
      min-height: 80px;
    }

    .form-range {
      width: 100%;
      margin: var(--spacing-2) 0;
    }

    .range-value {
      text-align: center;
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
      margin-top: var(--spacing-1);
    }

    .modal-actions {
      display: flex;
      gap: var(--spacing-3);
      justify-content: flex-end;
      margin-top: var(--spacing-4);
      padding-top: var(--spacing-4);
      border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .btn-secondary {
      padding: var(--spacing-2) var(--spacing-4);
      background: transparent;
      color: var(--color-text-secondary);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .btn-secondary:hover {
      background: rgba(255, 255, 255, 0.05);
      color: var(--color-text-primary);
    }

    .btn-primary {
      padding: var(--spacing-2) var(--spacing-4);
      background: var(--color-primary);
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .btn-primary:hover {
      background: var(--color-primary-dark);
    }

    .btn-primary:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .wheel-container {
      position: relative;
      width: 100%;
      max-width: 500px;
      aspect-ratio: 1;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .wheel-background {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0.3;
      filter: grayscale(100%) brightness(0.7);
      z-index: 0;
    }

    .wheel-foreground {
      position: relative;
      z-index: 1;
      width: 100%;
      height: 100%;
    }

    .wheel-placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: rgba(255, 255, 255, 0.7);
      text-align: center;
      padding: var(--spacing-8);
    }

    .wheel-placeholder-icon {
      width: 64px;
      height: 64px;
      margin-bottom: var(--spacing-4);
      opacity: 0.5;
    }

    .chat-section {
      background: rgba(255, 255, 255, 0.95);
      border-radius: var(--radius-2xl);
      padding: var(--spacing-6);
      box-shadow: var(--shadow-xl);
      backdrop-filter: blur(10px);
    }

    .error-banner {
      background: var(--color-error);
      color: white;
      padding: var(--spacing-4);
      border-radius: var(--radius-lg);
      margin-bottom: var(--spacing-4);
      display: flex;
      align-items: center;
      gap: var(--spacing-3);
    }

    .error-banner button {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      padding: var(--spacing-2) var(--spacing-3);
      font-size: var(--font-size-sm);
    }

    .progress-bar-container {
      margin-bottom: var(--spacing-4, 16px);
      padding: 0 var(--spacing-2, 8px);
      animation: slideDown 0.3s ease-out;
    }

    @keyframes slideDown {
      from {
        opacity: 0;
        transform: translateY(-10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .connection-error-banner {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      background: #dc3545; /* Red background for connection errors */
      color: white;
      padding: var(--spacing-3);
      text-align: center;
      z-index: 2000;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      font-weight: 500;
    }

    /* Enjoy Overlay Styles */
    .enjoy-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(255, 255, 255, 0.4);
      backdrop-filter: blur(10px);
      z-index: 3000;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: var(--color-text-primary);
      text-align: center;
      animation: enjoyOverlayFadeIn 0.5s ease-out;
    }

    @keyframes enjoyOverlayFadeIn {
      0% {
        opacity: 0;
        transform: scale(0.95);
      }
      100% {
        opacity: 1;
        transform: scale(1);
      }
    }

    .enjoy-overlay-content {
      max-width: 600px;
      padding: var(--spacing-8);
      background: rgba(255, 255, 255, 0.9);
      border-radius: var(--radius-2xl);
      border: 1px solid rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(20px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .enjoy-title {
      font-size: 3rem;
      font-weight: 700;
      margin-bottom: var(--spacing-6);
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-shadow: 0 0 30px rgba(102, 126, 234, 0.5);
    }

    .enjoy-activity-info {
      margin-bottom: var(--spacing-6);
    }

    .enjoy-activity-name {
      font-size: 2rem;
      font-weight: 600;
      margin-bottom: var(--spacing-3);
      color: var(--color-text-primary);
    }

    .enjoy-activity-description {
      font-size: 1.2rem;
      line-height: 1.6;
      color: var(--color-text-secondary);
      margin-bottom: var(--spacing-4);
    }

    .enjoy-activity-details {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: var(--spacing-3);
      margin-bottom: var(--spacing-6);
    }

    .enjoy-detail-item {
      background: rgba(102, 126, 234, 0.1);
      padding: var(--spacing-3);
      border-radius: var(--radius-lg);
      border: 1px solid rgba(102, 126, 234, 0.2);
    }

    .enjoy-detail-label {
      font-size: 0.9rem;
      color: var(--color-text-secondary);
      margin-bottom: var(--spacing-1);
    }

    .enjoy-detail-value {
      font-size: 1.1rem;
      font-weight: 600;
      color: var(--color-text-primary);
    }

    .enjoy-hourglass {
      font-size: 3rem;
      margin: var(--spacing-6) 0;
      animation: rotateHourglass 2s linear infinite;
    }

    @keyframes rotateHourglass {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .enjoy-timer {
      font-size: 1.2rem;
      color: var(--color-text-secondary);
      margin-bottom: var(--spacing-4);
    }

    .enjoy-close-btn {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      padding: var(--spacing-3) var(--spacing-6);
      border-radius: var(--radius-lg);
      font-size: 1.1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .enjoy-close-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    }

    /* Post-Activity Feedback Modal Styles */
    .post-activity-feedback-modal {
      max-width: 600px;
      width: 90%;
      max-height: 90vh;
      overflow-y: auto;
    }

    /* Update modal overlay to have white background */
    .modal-overlay {
      background: rgba(255, 255, 255, 0.4);
      backdrop-filter: blur(10px);
    }

    .activity-summary {
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
      border-radius: var(--radius-lg);
      padding: var(--spacing-4);
      margin-bottom: var(--spacing-6);
      border: 1px solid rgba(102, 126, 234, 0.2);
    }

    .activity-info .activity-name {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: var(--spacing-2);
      color: var(--color-text-primary);
    }

    .activity-info .activity-description {
      font-size: 1rem;
      line-height: 1.5;
      color: var(--color-text-secondary);
    }

    .feedback-message {
      font-size: 1.1rem;
      line-height: 1.6;
      color: var(--color-text-secondary);
      margin-bottom: var(--spacing-6);
      text-align: center;
      font-style: italic;
    }

    .feedback-form {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-6);
    }

    .rating-group {
      background: rgba(255, 255, 255, 0.05);
      border-radius: var(--radius-lg);
      padding: var(--spacing-4);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .rating-group .form-label {
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: var(--spacing-3);
      color: var(--color-text-primary);
    }

    .rating-options {
      display: flex;
      flex-wrap: wrap;
      gap: var(--spacing-2);
      justify-content: space-between;
    }

    .rating-option {
      flex: 1;
      min-width: 100px;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: var(--spacing-3);
      border-radius: var(--radius-md);
      border: 2px solid rgba(255, 255, 255, 0.1);
      background: rgba(255, 255, 255, 0.05);
      cursor: pointer;
      transition: all 0.3s ease;
      text-align: center;
    }

    .rating-option:hover {
      border-color: rgba(102, 126, 234, 0.5);
      background: rgba(102, 126, 234, 0.1);
      transform: translateY(-2px);
    }

    .rating-option input[type="radio"] {
      display: none;
    }

    .rating-option input[type="radio"]:checked + .rating-text {
      color: #667eea;
      font-weight: 600;
    }

    .rating-option:has(input[type="radio"]:checked) {
      border-color: #667eea;
      background: rgba(102, 126, 234, 0.2);
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .rating-text {
      font-size: 0.9rem;
      color: var(--color-text-secondary);
      transition: all 0.3s ease;
      margin-top: var(--spacing-1);
    }

    @media (max-width: 768px) {
      .rating-options {
        flex-direction: column;
      }

      .rating-option {
        min-width: auto;
        flex-direction: row;
        justify-content: flex-start;
        gap: var(--spacing-2);
      }

      .rating-text {
        margin-top: 0;
      }
    }

    .loading-banner {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: var(--spacing-3) var(--spacing-4);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-3);
      z-index: var(--z-sticky);
      font-size: var(--font-size-sm);
      font-weight: 500;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transform: translateY(-100%);
      animation: slideDown 0.3s ease-out forwards;
    }

    .loading-banner .dots-animation {
      display: flex;
      gap: 4px;
      align-items: center;
    }

    .loading-banner .dots-animation span {
      width: 6px;
      height: 6px;
      background: white;
      border-radius: 50%;
      animation: dots-bounce 1.4s infinite ease-in-out;
    }

    .loading-banner .dots-animation span:nth-child(1) {
      animation-delay: -0.32s;
    }

    .loading-banner .dots-animation span:nth-child(2) {
      animation-delay: -0.16s;
    }

    .loading-banner .dots-animation span:nth-child(3) {
      animation-delay: 0s;
    }

    @keyframes slideDown {
      to {
        transform: translateY(0);
      }
    }

    @keyframes dots-bounce {
      0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.7;
      }
      40% {
        transform: scale(1);
        opacity: 1;
      }
    }

    /* Mobile responsive */
    @media (max-width: 768px) {
      .main-content {
        padding: var(--spacing-4);
        gap: var(--spacing-4);
      }

      .header {
        padding: var(--spacing-3) var(--spacing-4);
      }

      .logo {
        font-size: var(--font-size-lg);
      }

      .chat-section {
        padding: var(--spacing-4);
      }
    }
  `;m([c()],u.prototype,"messages",2);m([c()],u.prototype,"wheelData",2);m([c()],u.prototype,"wsConnected",2);m([c()],u.prototype,"isLoading",2);m([c()],u.prototype,"error",2);m([c()],u.prototype,"isAuthenticated",2);m([c()],u.prototype,"showDebugPanel",2);m([c()],u.prototype,"currentMode",2);m([c()],u.prototype,"debugUserId",2);m([c()],u.prototype,"debugLLMConfigId",2);m([c()],u.prototype,"timeAvailable",2);m([c()],u.prototype,"showProgressBar",2);m([c()],u.prototype,"currentProgressTrackerId",2);m([c()],u.prototype,"progressTitle",2);m([c()],u.prototype,"energyLevel",2);m([c()],u.prototype,"currentUser",2);m([c()],u.prototype,"connectionState",2);m([c()],u.prototype,"expandedActivities",2);m([c()],u.prototype,"showActivityModal",2);m([c()],u.prototype,"selectedActivityId",2);m([c()],u.prototype,"searchQuery",2);m([c()],u.prototype,"activityCatalog",2);m([c()],u.prototype,"showCreateActivityModal",2);m([c()],u.prototype,"newActivityForm",2);m([c()],u.prototype,"showProfileModal",2);m([c()],u.prototype,"currentUserProfile",2);m([c()],u.prototype,"showWinningModal",2);m([c()],u.prototype,"winningActivity",2);m([c()],u.prototype,"expandedProfileSections",2);m([c()],u.prototype,"editingProfileFields",2);m([c()],u.prototype,"showFeedbackModal",2);m([c()],u.prototype,"feedbackModalConfig",2);m([c()],u.prototype,"showAddActivityModal",2);m([c()],u.prototype,"showContractModal",2);m([c()],u.prototype,"showEnjoyOverlay",2);m([c()],u.prototype,"enjoyOverlayActivity",2);m([c()],u.prototype,"enjoyOverlayStartTime",2);m([c()],u.prototype,"showPostActivityFeedbackModal",2);m([c()],u.prototype,"postActivityFeedbackConfig",2);u=m([j("app-shell")],u);const ve=B.getInstance(),se=ve.getConfig();function Dt(){window.addEventListener("error",t=>{console.error("Global error:",t.error),be("An unexpected error occurred")}),window.addEventListener("unhandledrejection",t=>{console.error("Unhandled promise rejection:",t.reason),be("A network or loading error occurred")})}function be(t){const e=document.getElementById("error-fallback"),i=document.getElementById("loading-screen");if(e){e.style.display="block";const s=e.querySelector("p");s&&(t.includes("WebSocket")||t.includes("connection")?s.textContent="Unable to connect to server. The app will work in demo mode with sample data.":t.includes("WebGL")?s.textContent="Your browser doesn't support WebGL. Please try a different browser.":s.textContent=t)}i&&i.classList.add("hidden")}function It(){const t=document.getElementById("loading-screen"),e=document.querySelector("app-shell");t&&(t.classList.add("hidden"),setTimeout(()=>{t.style.display="none"},500)),e&&e.classList.add("loaded")}async function Lt(){try{const t=H.getInstance();t.initialize(se);const e=Y.getInstance(),i=V.getInstance();i.initialize(e,t);const s=F.getInstance();ve.isProductionMode()&&s.setupAutoRefresh(),se.debug.enabled&&(window.__GOALI_DEBUG__={stateManager:t,wsManager:e,messageHandler:i,configService:ve,authService:s,config:se}),console.log(`✅ Core services initialized successfully (${se.mode} mode)`)}catch(t){console.error("❌ Failed to initialize services:",t),console.warn("⚠️ Continuing with limited functionality")}}function Tt(){const t=["customElements","WebSocket","Promise","fetch","requestAnimationFrame"];for(const s of t)if(!(s in window))return console.error(`❌ Browser missing required feature: ${s}`),!1;const e=document.createElement("canvas");return e.getContext("webgl")||e.getContext("experimental-webgl")?!0:(console.error("❌ WebGL not supported"),!1)}async function Me(){console.log("🚀 Initializing Goali frontend...");try{if(!Tt())throw new Error("Browser not compatible");Dt(),await Lt(),await Promise.all([customElements.whenDefined("app-shell"),customElements.whenDefined("game-wheel"),customElements.whenDefined("chat-interface"),customElements.whenDefined("debug-panel"),customElements.whenDefined("login-form")]),It(),console.log("✅ Goali frontend initialized successfully")}catch(t){console.error("❌ Failed to initialize app:",t),be(t instanceof Error?t.message:"Failed to load application")}}document.readyState==="loading"?document.addEventListener("DOMContentLoaded",Me):Me();
//# sourceMappingURL=main-fCCIGbmt.js.map
